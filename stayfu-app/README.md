# StayFu - Short-Term Rental Management App

A comprehensive property management application for short-term rental hosts, built with React, Vite, Tailwind CSS, and designed for easy deployment to Vercel.

## Features

### Core Functionality
- **Property Management**: Add, edit, and organize your rental properties
- **Inventory Tracking**: Monitor stock levels, set reorder points, and track supplies
- **Maintenance Management**: Schedule tasks, assign to service providers, track completion
- **Damage Reporting**: Document and track property damage with photos and cost estimates
- **Purchase Requests**: Create shopping lists and track procurement needs

### User Management
- **Authentication**: Secure login and registration system
- **Role-based Access**: Property Manager, Service Provider, Staff roles
- **User Profiles**: Customizable profiles with theme preferences

### Modern UI/UX
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Dark/Light Themes**: User-selectable theme preferences
- **Intuitive Navigation**: Clean, modern interface with easy navigation

## Technology Stack

- **Frontend**: React 19 with Vite
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Custom components with Lucide React icons
- **State Management**: React Query for data fetching and caching
- **Routing**: React Router with hash-based routing
- **Database**: In-memory storage (development) → Supabase (production)
- **Deployment**: Vercel-ready configuration

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

### First Time Setup

1. Register a new account or login
2. Go to Settings → Data Management
3. Click "Create Sample Data" to populate the app with test data
4. Explore the different modules: Properties, Inventory, Maintenance, etc.

## Development Status

✅ **Completed Features:**
- Authentication and user management
- Property CRUD operations
- Inventory management with low stock alerts
- Maintenance task tracking
- Damage reporting system
- Purchase request management
- Responsive UI with dark/light themes
- Sample data generation for testing

🚧 **In Progress:**
- Supabase integration for production
- Chrome extension integration
- Image upload functionality

📋 **Planned Features:**
- Service provider invitations
- Email notifications
- Calendar integration (iCal)
- PDF report generation
- Advanced analytics
