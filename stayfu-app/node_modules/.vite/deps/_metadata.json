{"hash": "e61a8407", "configHash": "af0a2e50", "lockfileHash": "3317c0e1", "browserHash": "b4f0b510", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5fcfcd6f", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "9c71b70b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "47ec81a1", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f2fa2a96", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "2d1a8283", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "841509f0", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b5893afe", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "fc60cd6d", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "e0164ec7", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "2f2eadd7", "needsInterop": false}}, "chunks": {"chunk-OBHQJEAR": {"file": "chunk-OBHQJEAR.js"}, "chunk-ALF5RGTR": {"file": "chunk-ALF5RGTR.js"}, "chunk-6L6DU33K": {"file": "chunk-6L6DU33K.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}