{"hash": "25c68610", "configHash": "af0a2e50", "lockfileHash": "7dae0b6b", "browserHash": "7ec1ee03", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "20fe492d", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "6e447901", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "26fafaba", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "2d92bcc2", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "e4eb3036", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "48562d00", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "4351b262", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "9ee23095", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "24c2a256", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "f61be5ca", "needsInterop": false}}, "chunks": {"chunk-OBHQJEAR": {"file": "chunk-OBHQJEAR.js"}, "chunk-ALF5RGTR": {"file": "chunk-ALF5RGTR.js"}, "chunk-6L6DU33K": {"file": "chunk-6L6DU33K.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}