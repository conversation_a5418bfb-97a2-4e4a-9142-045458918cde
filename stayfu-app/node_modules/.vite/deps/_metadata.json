{"hash": "25c68610", "configHash": "af0a2e50", "lockfileHash": "7dae0b6b", "browserHash": "7ec1ee03", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "8faa644a", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "56987301", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "4ca10727", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "e46c6dcf", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "7fdb6e94", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "3679e838", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "64ba4227", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "462ba1ed", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "4beb55d0", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "f04ebd99", "needsInterop": false}}, "chunks": {"chunk-OBHQJEAR": {"file": "chunk-OBHQJEAR.js"}, "chunk-ALF5RGTR": {"file": "chunk-ALF5RGTR.js"}, "chunk-6L6DU33K": {"file": "chunk-6L6DU33K.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}