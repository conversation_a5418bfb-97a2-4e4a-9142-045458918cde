import { useState, useEffect } from 'react'
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { createTables, createTriggers } from './lib/schema.js'
import { initDatabase } from './lib/database.js'
import { authStorage } from './lib/auth.js'

// Components
import Layout from './components/Layout'
import Login from './pages/Login'
import Register from './pages/Register'
import Dashboard from './pages/Dashboard'
import Properties from './pages/Properties'
import Inventory from './pages/Inventory'
import Maintenance from './pages/Maintenance'
import Damages from './pages/Damages'
import PurchaseRequests from './pages/PurchaseRequests'
import ServiceProviders from './pages/ServiceProviders'
// import Teams from './pages/Teams'
import Bookings from './pages/Bookings'
import Settings from './pages/Settings'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
    },
  },
})

// Protected Route component
function ProtectedRoute({ children }) {
  const isAuthenticated = authStorage.isAuthenticated()
  return isAuthenticated ? children : <Navigate to="/login" replace />
}

// Public Route component (redirect to dashboard if authenticated)
function PublicRoute({ children }) {
  const isAuthenticated = authStorage.isAuthenticated()
  return !isAuthenticated ? children : <Navigate to="/dashboard" replace />
}

function App() {
  const [isDbInitialized, setIsDbInitialized] = useState(false)

  useEffect(() => {
    // Initialize database on app start
    const initializeDatabase = async () => {
      try {
        initDatabase()
        createTables()
        createTriggers()
        setIsDbInitialized(true)
        console.log('Database initialized successfully')
      } catch (error) {
        console.error('Failed to initialize database:', error)
      }
    }

    initializeDatabase()
  }, [])

  if (!isDbInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Initializing StayFu...</p>
        </div>
      </div>
    )
  }

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          } />
          <Route path="/register" element={
            <PublicRoute>
              <Register />
            </PublicRoute>
          } />

          {/* Protected routes */}
          <Route path="/" element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="properties" element={<Properties />} />
            <Route path="inventory" element={<Inventory />} />
            <Route path="maintenance" element={<Maintenance />} />
            <Route path="damages" element={<Damages />} />
            <Route path="purchase-requests" element={<PurchaseRequests />} />
            <Route path="service-providers" element={<ServiceProviders />} />
            {/* <Route path="teams" element={<Teams />} /> */}
            <Route path="bookings" element={<Bookings />} />
            <Route path="settings" element={<Settings />} />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </QueryClientProvider>
  )
}

export default App
