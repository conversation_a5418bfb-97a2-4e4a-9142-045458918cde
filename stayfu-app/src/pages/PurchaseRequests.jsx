import { useState, useEffect } from 'react'
import { Plus, ShoppingCart, Search, Edit, Trash2, ExternalLink, Package, DollarSign } from 'lucide-react'
import { authStorage } from '../lib/auth.js'
import { executeQuery, executeUpdate } from '../lib/database.js'
import { cn } from '../lib/utils.js'

const STATUS_OPTIONS = ['Requested', 'Approved', 'Ordered', 'Received', 'Cancelled']

export default function PurchaseRequests() {
  const [purchaseRequests, setPurchaseRequests] = useState([])
  const [properties, setProperties] = useState([])
  const [collections, setCollections] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [propertyFilter, setPropertyFilter] = useState('')
  const [formData, setFormData] = useState({
    property_id: '',
    collection_id: '',
    item_name: '',
    quantity: 1,
    estimated_price_each: '',
    amazon_product_link: '',
    notes: '',
    status: 'Requested'
  })
  const user = authStorage.getUser()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      // Load properties
      const propertiesResult = executeQuery(
        'SELECT * FROM properties WHERE owner_user_id = ? ORDER BY name',
        [user.id]
      )
      setProperties(propertiesResult)

      // Load collections
      const collectionsResult = executeQuery(
        'SELECT c.*, p.name as property_name FROM collections c JOIN properties p ON c.property_id = p.id WHERE p.owner_user_id = ? ORDER BY p.name, c.name',
        [user.id]
      )
      setCollections(collectionsResult)

      // Load purchase requests
      loadPurchaseRequests()
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadPurchaseRequests = async () => {
    try {
      let query = `
        SELECT pr.*, p.name as property_name, c.name as collection_name,
               u.full_name as requested_by_name
        FROM purchase_requests pr
        JOIN properties p ON pr.property_id = p.id
        LEFT JOIN collections c ON pr.collection_id = c.id
        LEFT JOIN users u ON pr.requested_by_user_id = u.id
        WHERE p.owner_user_id = ?
      `
      const params = [user.id]

      if (propertyFilter) {
        query += ' AND pr.property_id = ?'
        params.push(propertyFilter)
      }

      if (statusFilter) {
        query += ' AND pr.status = ?'
        params.push(statusFilter)
      }

      if (searchTerm) {
        query += ' AND (pr.item_name LIKE ? OR pr.notes LIKE ?)'
        params.push(`%${searchTerm}%`, `%${searchTerm}%`)
      }

      query += ' ORDER BY pr.created_at DESC'

      const result = executeQuery(query, params)
      setPurchaseRequests(result)
    } catch (error) {
      console.error('Failed to load purchase requests:', error)
    }
  }

  useEffect(() => {
    if (!isLoading) {
      loadPurchaseRequests()
    }
  }, [searchTerm, statusFilter, propertyFilter, isLoading])

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      executeUpdate(
        `INSERT INTO purchase_requests (property_id, collection_id, requested_by_user_id, item_name, quantity, estimated_price_each, amazon_product_link, notes, status)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          formData.property_id,
          formData.collection_id || null,
          user.id,
          formData.item_name,
          parseInt(formData.quantity),
          formData.estimated_price_each ? parseFloat(formData.estimated_price_each) : null,
          formData.amazon_product_link || null,
          formData.notes,
          formData.status
        ]
      )

      setFormData({
        property_id: '',
        collection_id: '',
        item_name: '',
        quantity: 1,
        estimated_price_each: '',
        amazon_product_link: '',
        notes: '',
        status: 'Requested'
      })
      setShowAddForm(false)
      loadPurchaseRequests()
    } catch (error) {
      console.error('Failed to add purchase request:', error)
    }
  }

  const handleChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const getAvailableCollections = () => {
    if (!formData.property_id) return []
    return collections.filter(c => c.property_id === formData.property_id)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Requested': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'Approved': return 'text-green-600 bg-green-50 border-green-200'
      case 'Ordered': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'Received': return 'text-green-600 bg-green-50 border-green-200'
      case 'Cancelled': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-foreground">Purchase Requests</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className={cn(
            "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
            "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
          )}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Request
        </button>
      </div>

      {/* Filters */}
      <div className="bg-card border border-border rounded-lg p-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-foreground mb-1">
              Search Requests
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={cn(
                  "pl-10 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                  "bg-background text-foreground"
                )}
                placeholder="Search by item name or notes"
              />
            </div>
          </div>
          <div>
            <label htmlFor="property-filter" className="block text-sm font-medium text-foreground mb-1">
              Property
            </label>
            <select
              id="property-filter"
              value={propertyFilter}
              onChange={(e) => setPropertyFilter(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                "bg-background text-foreground"
              )}
            >
              <option value="">All Properties</option>
              {properties.map((property) => (
                <option key={property.id} value={property.id}>
                  {property.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-foreground mb-1">
              Status
            </label>
            <select
              id="status-filter"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                "bg-background text-foreground"
              )}
            >
              <option value="">All Statuses</option>
              {STATUS_OPTIONS.map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('')
                setPropertyFilter('')
                setStatusFilter('')
              }}
              className="px-4 py-2 border border-border rounded-md text-sm font-medium text-foreground hover:bg-accent w-full"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Add Purchase Request Form */}
      {showAddForm && (
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">Add New Purchase Request</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="property_id" className="block text-sm font-medium text-foreground">
                  Property *
                </label>
                <select
                  id="property_id"
                  name="property_id"
                  required
                  value={formData.property_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                >
                  <option value="">Select a property</option>
                  {properties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="collection_id" className="block text-sm font-medium text-foreground">
                  Collection
                </label>
                <select
                  id="collection_id"
                  name="collection_id"
                  value={formData.collection_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                >
                  <option value="">No collection</option>
                  {getAvailableCollections().map((collection) => (
                    <option key={collection.id} value={collection.id}>
                      {collection.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div className="sm:col-span-2">
                <label htmlFor="item_name" className="block text-sm font-medium text-foreground">
                  Item Name *
                </label>
                <input
                  type="text"
                  id="item_name"
                  name="item_name"
                  required
                  value={formData.item_name}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                  placeholder="Enter item name"
                />
              </div>
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-foreground">
                  Quantity *
                </label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  required
                  min="1"
                  value={formData.quantity}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                  placeholder="1"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="estimated_price_each" className="block text-sm font-medium text-foreground">
                  Estimated Price Each
                </label>
                <input
                  type="number"
                  id="estimated_price_each"
                  name="estimated_price_each"
                  min="0"
                  step="0.01"
                  value={formData.estimated_price_each}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                  placeholder="0.00"
                />
              </div>
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-foreground">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                >
                  {STATUS_OPTIONS.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div>
              <label htmlFor="amazon_product_link" className="block text-sm font-medium text-foreground">
                Amazon Product Link
              </label>
              <input
                type="url"
                id="amazon_product_link"
                name="amazon_product_link"
                value={formData.amazon_product_link}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                  "bg-background text-foreground"
                )}
                placeholder="https://amazon.com/..."
              />
            </div>
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-foreground">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                rows={3}
                value={formData.notes}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                  "bg-background text-foreground"
                )}
                placeholder="Additional notes or specifications"
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 border border-border rounded-md text-sm font-medium text-foreground hover:bg-accent"
              >
                Cancel
              </button>
              <button
                type="submit"
                className={cn(
                  "px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                  "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
                )}
              >
                Add Request
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Purchase Requests List */}
      {purchaseRequests.length === 0 ? (
        <div className="text-center py-12">
          <ShoppingCart className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-sm font-medium text-foreground">No purchase requests</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            Get started by adding your first purchase request.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddForm(true)}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
              )}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Request
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {purchaseRequests.map((request) => (
            <div key={request.id} className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <Package className="h-5 w-5 text-blue-500" />
                    <h3 className="text-lg font-medium text-foreground">{request.item_name}</h3>
                    <span className={cn(
                      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",
                      getStatusColor(request.status)
                    )}>
                      {request.status}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-muted-foreground">
                    {request.property_name}
                    {request.collection_name && ` - ${request.collection_name}`}
                  </p>
                  {request.notes && (
                    <p className="mt-2 text-sm text-foreground">{request.notes}</p>
                  )}

                  <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Package className="h-4 w-4 mr-1" />
                      Quantity: {request.quantity}
                    </div>
                    {request.estimated_price_each && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <DollarSign className="h-4 w-4 mr-1" />
                        ${parseFloat(request.estimated_price_each).toFixed(2)} each
                      </div>
                    )}
                    {request.amazon_product_link && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <ExternalLink className="h-4 w-4 mr-1" />
                        <a
                          href={request.amazon_product_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:text-primary/80"
                        >
                          Amazon Link
                        </a>
                      </div>
                    )}
                    <div className="text-sm text-muted-foreground">
                      Requested: {new Date(request.created_at).toLocaleDateString()}
                    </div>
                  </div>

                  {(request.estimated_price_each && request.quantity) && (
                    <div className="mt-2 text-sm font-medium text-foreground">
                      Total Estimated: ${(parseFloat(request.estimated_price_each) * parseInt(request.quantity)).toFixed(2)}
                    </div>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button className="p-2 text-muted-foreground hover:text-foreground">
                    <Edit className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-muted-foreground hover:text-destructive">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
