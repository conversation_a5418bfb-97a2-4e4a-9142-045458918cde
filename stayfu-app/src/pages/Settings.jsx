import { useState, useEffect } from 'react'
import { Save, User, Palette, Database, Trash2 } from 'lucide-react'
import { authStorage, updateUserProfile } from '../lib/auth.js'
import { createSeedData, hasUserData } from '../lib/seedData.js'
import { getDatabase } from '../lib/database.js'
import { cn } from '../lib/utils.js'

export default function Settings() {
  const [formData, setFormData] = useState({
    full_name: '',
    theme_preference: 'light'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [dataMessage, setDataMessage] = useState('')
  const user = authStorage.getUser()

  useEffect(() => {
    if (user) {
      setFormData({
        full_name: user.full_name || '',
        theme_preference: user.theme_preference || 'light'
      })
    }
  }, [user])

  const handleChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage('')

    try {
      const updatedUser = await updateUserProfile(user.id, formData)
      authStorage.setUser(updatedUser)
      setMessage('Settings updated successfully!')
    } catch (error) {
      setMessage('Failed to update settings: ' + error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateSampleData = async () => {
    setDataMessage('')
    try {
      const success = createSeedData(user.id)
      if (success) {
        setDataMessage('Sample data created successfully!')
      } else {
        setDataMessage('Failed to create sample data')
      }
    } catch (error) {
      setDataMessage('Error creating sample data: ' + error.message)
    }
  }

  const handleClearAllData = async () => {
    if (!confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      return
    }

    setDataMessage('')
    try {
      const db = getDatabase()
      // Clear all data
      Object.keys(db).forEach(table => {
        if (table !== 'users') {
          db[table] = []
        }
      })
      // Save to localStorage
      localStorage.setItem('stayfu_database', JSON.stringify(db))
      setDataMessage('All data cleared successfully!')
    } catch (error) {
      setDataMessage('Error clearing data: ' + error.message)
    }
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-foreground">Settings</h1>
      
      <div className="bg-card border border-border rounded-lg p-6">
        <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center">
          <User className="h-5 w-5 mr-2" />
          Profile Settings
        </h2>
        
        {message && (
          <div className={cn(
            "mb-4 p-4 rounded-md",
            message.includes('successfully') 
              ? "bg-green-50 border border-green-200 text-green-800"
              : "bg-red-50 border border-red-200 text-red-800"
          )}>
            {message}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="full_name" className="block text-sm font-medium text-foreground">
              Full Name
            </label>
            <input
              type="text"
              id="full_name"
              name="full_name"
              value={formData.full_name}
              onChange={handleChange}
              className={cn(
                "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                "bg-background text-foreground"
              )}
              placeholder="Enter your full name"
            />
          </div>
          
          <div>
            <label htmlFor="theme_preference" className="block text-sm font-medium text-foreground">
              <Palette className="h-4 w-4 inline mr-1" />
              Theme Preference
            </label>
            <select
              id="theme_preference"
              name="theme_preference"
              value={formData.theme_preference}
              onChange={handleChange}
              className={cn(
                "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                "bg-background text-foreground"
              )}
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
            </select>
          </div>
          
          <div className="pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring",
                "disabled:opacity-50 disabled:cursor-not-allowed"
              )}
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Settings
            </button>
          </div>
        </form>
      </div>
      
      <div className="bg-card border border-border rounded-lg p-6">
        <h2 className="text-lg font-semibold text-foreground mb-4">Account Information</h2>
        <div className="space-y-2">
          <div>
            <span className="text-sm font-medium text-foreground">Email:</span>
            <span className="ml-2 text-sm text-muted-foreground">{user?.email}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-foreground">Role:</span>
            <span className="ml-2 text-sm text-muted-foreground capitalize">
              {user?.role?.replace('_', ' ')}
            </span>
          </div>
          <div>
            <span className="text-sm font-medium text-foreground">Member since:</span>
            <span className="ml-2 text-sm text-muted-foreground">
              {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
            </span>
          </div>
        </div>
      </div>

      {/* Data Management */}
      <div className="bg-card border border-border rounded-lg p-6">
        <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center">
          <Database className="h-5 w-5 mr-2" />
          Data Management
        </h2>

        {dataMessage && (
          <div className={cn(
            "mb-4 p-4 rounded-md",
            dataMessage.includes('successfully')
              ? "bg-green-50 border border-green-200 text-green-800"
              : "bg-red-50 border border-red-200 text-red-800"
          )}>
            {dataMessage}
          </div>
        )}

        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-foreground mb-2">Sample Data</h3>
            <p className="text-sm text-muted-foreground mb-3">
              Create sample properties, inventory items, and tasks to test the application.
            </p>
            <button
              onClick={handleCreateSampleData}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
              )}
            >
              <Database className="h-4 w-4 mr-2" />
              Create Sample Data
            </button>
          </div>

          <div className="border-t border-border pt-4">
            <h3 className="text-sm font-medium text-foreground mb-2">Clear All Data</h3>
            <p className="text-sm text-muted-foreground mb-3">
              Remove all properties, inventory, tasks, and other data. This action cannot be undone.
            </p>
            <button
              onClick={handleClearAllData}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                "text-destructive-foreground bg-destructive hover:bg-destructive/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
              )}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All Data
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
