import { useState, useEffect } from 'react'
import { Plus, Wrench, Search, Edit, Trash2, Clock, User, AlertTriangle, Download } from 'lucide-react'
import { authStorage } from '../lib/auth.js'
import { executeQuery, executeUpdate } from '../lib/database.js'
import { cn } from '../lib/utils.js'
import { pdfService } from '../lib/pdfService.js'
import { realTimeService } from '../lib/realTimeService.js'
import { useNotifications } from '../components/NotificationCenter.jsx'
import AdvancedSearch from '../components/AdvancedSearch.jsx'

const PRIORITY_OPTIONS = ['Low', 'Medium', 'High']
const STATUS_OPTIONS = ['New', 'In Progress', 'Completed', 'Blocked']

export default function Maintenance() {
  const [maintenanceTasks, setMaintenanceTasks] = useState([])
  const [properties, setProperties] = useState([])
  const [serviceProviders, setServiceProviders] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('')
  const [propertyFilter, setPropertyFilter] = useState('')
  const [formData, setFormData] = useState({
    property_id: '',
    title: '',
    description: '',
    priority: 'Medium',
    status: 'New',
    due_date: '',
    assigned_to_service_provider_id: ''
  })
  const user = authStorage.getUser()
  const { showSuccess, showError } = useNotifications()

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    // Subscribe to real-time maintenance updates
    const unsubscribe = realTimeService.subscribe('maintenance', (data) => {
      if (data.type === 'task_updated') {
        loadMaintenanceTasks() // Refresh the list
      }
    })

    return unsubscribe
  }, [])

  const loadData = async () => {
    try {
      // Load properties
      const propertiesResult = executeQuery(
        'SELECT * FROM properties WHERE owner_user_id = ? ORDER BY name',
        [user.id]
      )
      setProperties(propertiesResult)

      // Load service providers
      const serviceProvidersResult = executeQuery(
        'SELECT * FROM service_providers WHERE invited_by_user_id = ? ORDER BY name',
        [user.id]
      )
      setServiceProviders(serviceProvidersResult)

      // Load maintenance tasks
      loadMaintenanceTasks()
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadMaintenanceTasks = async () => {
    try {
      let query = `
        SELECT m.*, p.name as property_name, sp.name as service_provider_name,
               u.full_name as reported_by_name
        FROM maintenance_tasks m
        JOIN properties p ON m.property_id = p.id
        LEFT JOIN service_providers sp ON m.assigned_to_service_provider_id = sp.id
        LEFT JOIN users u ON m.reported_by_user_id = u.id
        WHERE p.owner_user_id = ?
      `
      const params = [user.id]

      if (propertyFilter) {
        query += ' AND m.property_id = ?'
        params.push(propertyFilter)
      }

      if (statusFilter) {
        query += ' AND m.status = ?'
        params.push(statusFilter)
      }

      if (priorityFilter) {
        query += ' AND m.priority = ?'
        params.push(priorityFilter)
      }

      if (searchTerm) {
        query += ' AND (m.title LIKE ? OR m.description LIKE ?)'
        params.push(`%${searchTerm}%`, `%${searchTerm}%`)
      }

      query += ' ORDER BY CASE m.priority WHEN "High" THEN 1 WHEN "Medium" THEN 2 ELSE 3 END, m.due_date ASC, m.created_at DESC'

      const result = executeQuery(query, params)
      setMaintenanceTasks(result)
    } catch (error) {
      console.error('Failed to load maintenance tasks:', error)
    }
  }

  useEffect(() => {
    if (!isLoading) {
      loadMaintenanceTasks()
    }
  }, [searchTerm, statusFilter, priorityFilter, propertyFilter, isLoading])

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      executeUpdate(
        `INSERT INTO maintenance_tasks (property_id, reported_by_user_id, title, description, priority, status, due_date, assigned_to_service_provider_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          formData.property_id,
          user.id,
          formData.title,
          formData.description,
          formData.priority,
          formData.status,
          formData.due_date || null,
          formData.assigned_to_service_provider_id || null
        ]
      )

      setFormData({
        property_id: '',
        title: '',
        description: '',
        priority: 'Medium',
        status: 'New',
        due_date: '',
        assigned_to_service_provider_id: ''
      })
      setShowAddForm(false)
      loadMaintenanceTasks()

      // Show success notification
      showSuccess('Task Created', `Maintenance task "${formData.title}" has been created successfully`)

      // Broadcast real-time update
      realTimeService.broadcastUpdate('maintenance', 'task_created', {
        title: formData.title,
        priority: formData.priority,
        property_id: formData.property_id
      })
    } catch (error) {
      console.error('Failed to add maintenance task:', error)
      showError('Error', 'Failed to create maintenance task. Please try again.')
    }
  }

  const handleChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const handleExportReport = () => {
    const propertyName = selectedProperty
      ? properties.find(p => p.id === selectedProperty)?.name || 'Selected Property'
      : 'All Properties'

    pdfService.exportMaintenanceReport(maintenanceTasks, propertyName)
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'High': return 'text-red-600 bg-red-50 border-red-200'
      case 'Medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'Low': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'New': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'In Progress': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'Completed': return 'text-green-600 bg-green-50 border-green-200'
      case 'Blocked': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const isOverdue = (task) => {
    if (!task.due_date || task.status === 'Completed') return false
    return new Date(task.due_date) < new Date()
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-50 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-50 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Maintenance Management</h1>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleExportReport}
            className={cn(
              "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md",
              "text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            )}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className={cn(
              "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
              "text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            )}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Task
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-900 mb-1">
              Search Tasks
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={cn(
                  "pl-10 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "bg-white text-gray-900"
                )}
                placeholder="Search by title or description"
              />
            </div>
          </div>
          <div>
            <label htmlFor="property-filter" className="block text-sm font-medium text-gray-900 mb-1">
              Property
            </label>
            <select
              id="property-filter"
              value={propertyFilter}
              onChange={(e) => setPropertyFilter(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "bg-white text-gray-900"
              )}
            >
              <option value="">All Properties</option>
              {properties.map((property) => (
                <option key={property.id} value={property.id}>
                  {property.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-900 mb-1">
              Status
            </label>
            <select
              id="status-filter"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "bg-white text-gray-900"
              )}
            >
              <option value="">All Statuses</option>
              {STATUS_OPTIONS.map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="priority-filter" className="block text-sm font-medium text-gray-900 mb-1">
              Priority
            </label>
            <select
              id="priority-filter"
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "bg-white text-gray-900"
              )}
            >
              <option value="">All Priorities</option>
              {PRIORITY_OPTIONS.map((priority) => (
                <option key={priority} value={priority}>
                  {priority}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('')
                setPropertyFilter('')
                setStatusFilter('')
                setPriorityFilter('')
              }}
              className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-50 w-full"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Add Task Form */}
      {showAddForm && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Add New Maintenance Task</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="property_id" className="block text-sm font-medium text-gray-900">
                  Property *
                </label>
                <select
                  id="property_id"
                  name="property_id"
                  required
                  value={formData.property_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                >
                  <option value="">Select a property</option>
                  {properties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-900">
                  Task Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  required
                  value={formData.title}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="Enter task title"
                />
              </div>
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-900">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "bg-white text-gray-900"
                )}
                placeholder="Enter task description"
              />
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
              <div>
                <label htmlFor="priority" className="block text-sm font-medium text-gray-900">
                  Priority
                </label>
                <select
                  id="priority"
                  name="priority"
                  value={formData.priority}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                >
                  {PRIORITY_OPTIONS.map((priority) => (
                    <option key={priority} value={priority}>
                      {priority}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-900">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                >
                  {STATUS_OPTIONS.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="due_date" className="block text-sm font-medium text-gray-900">
                  Due Date
                </label>
                <input
                  type="date"
                  id="due_date"
                  name="due_date"
                  value={formData.due_date}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                />
              </div>
              <div>
                <label htmlFor="assigned_to_service_provider_id" className="block text-sm font-medium text-gray-900">
                  Assign to Provider
                </label>
                <select
                  id="assigned_to_service_provider_id"
                  name="assigned_to_service_provider_id"
                  value={formData.assigned_to_service_provider_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                >
                  <option value="">Unassigned</option>
                  {serviceProviders.map((provider) => (
                    <option key={provider.id} value={provider.id}>
                      {provider.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className={cn(
                  "px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                  "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                )}
              >
                Add Task
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Maintenance Tasks List */}
      {maintenanceTasks.length === 0 ? (
        <div className="text-center py-12">
          <Wrench className="mx-auto h-12 w-12 text-gray-500" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No maintenance tasks</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first maintenance task.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddForm(true)}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              )}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Task
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {maintenanceTasks.map((task) => (
            <div key={task.id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-medium text-gray-900">{task.title}</h3>
                    <span className={cn(
                      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",
                      getPriorityColor(task.priority)
                    )}>
                      {task.priority}
                    </span>
                    <span className={cn(
                      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",
                      getStatusColor(task.status)
                    )}>
                      {task.status}
                    </span>
                    {isOverdue(task) && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Overdue
                      </span>
                    )}
                  </div>
                  <p className="mt-1 text-sm text-gray-500">{task.property_name}</p>
                  {task.description && (
                    <p className="mt-2 text-sm text-gray-900">{task.description}</p>
                  )}
                  <div className="mt-4 flex items-center space-x-6 text-sm text-gray-500">
                    {task.due_date && (
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        Due: {new Date(task.due_date).toLocaleDateString()}
                      </div>
                    )}
                    {task.service_provider_name && (
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        Assigned to: {task.service_provider_name}
                      </div>
                    )}
                    <div>
                      Created: {new Date(task.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button className="p-2 text-gray-500 hover:text-gray-900">
                    <Edit className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-gray-500 hover:text-red-600">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
