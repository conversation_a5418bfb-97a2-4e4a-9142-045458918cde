import { useState, useEffect } from 'react'
import { Plus, Building, MapPin, Edit, Trash2, FolderPlus, Folder, DollarSign } from 'lucide-react'
import { authStorage } from '../lib/auth.js'
import { executeQuery, executeUpdate } from '../lib/database.js'
import { cn } from '../lib/utils.js'

export default function Properties() {
  const [properties, setProperties] = useState([])
  const [collections, setCollections] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showCollectionForm, setShowCollectionForm] = useState(false)
  const [selectedProperty, setSelectedProperty] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    property_type: 'Apartment',
    description: ''
  })
  const [collectionFormData, setCollectionFormData] = useState({
    name: '',
    budget_amount: ''
  })
  const user = authStorage.getUser()

  useEffect(() => {
    loadProperties()
    loadCollections()
  }, [])

  const loadProperties = async () => {
    try {
      const result = executeQuery(
        'SELECT * FROM properties WHERE owner_user_id = ? ORDER BY created_at DESC',
        [user.id]
      )
      setProperties(result)
    } catch (error) {
      console.error('Failed to load properties:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadCollections = async () => {
    try {
      const result = executeQuery(
        `SELECT c.*, p.name as property_name 
         FROM collections c 
         JOIN properties p ON c.property_id = p.id 
         WHERE p.owner_user_id = ? 
         ORDER BY c.created_at DESC`,
        [user.id]
      )
      setCollections(result)
    } catch (error) {
      console.error('Failed to load collections:', error)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      executeUpdate(
        `INSERT INTO properties (owner_user_id, name, address, property_type, description) 
         VALUES (?, ?, ?, ?, ?)`,
        [user.id, formData.name, formData.address, formData.property_type, formData.description]
      )
      
      setFormData({ name: '', address: '', property_type: 'Apartment', description: '' })
      setShowAddForm(false)
      loadProperties()
    } catch (error) {
      console.error('Failed to add property:', error)
    }
  }

  const handleCollectionSubmit = async (e) => {
    e.preventDefault()
    try {
      executeUpdate(
        `INSERT INTO collections (property_id, name, budget_amount) 
         VALUES (?, ?, ?)`,
        [selectedProperty.id, collectionFormData.name, collectionFormData.budget_amount || null]
      )
      
      setCollectionFormData({ name: '', budget_amount: '' })
      setShowCollectionForm(false)
      setSelectedProperty(null)
      loadCollections()
    } catch (error) {
      console.error('Failed to add collection:', error)
    }
  }

  const handleChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const getPropertyCollections = (propertyId) => {
    return collections.filter(collection => collection.property_id === propertyId)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-50 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-50 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Properties</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className={cn(
            "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
            "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          )}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Property
        </button>
      </div>

      {/* Add Property Form */}
      {showAddForm && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Add New Property</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-900">
                  Property Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  value={formData.name}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="Enter property name"
                />
              </div>
              <div>
                <label htmlFor="property_type" className="block text-sm font-medium text-gray-900">
                  Property Type
                </label>
                <select
                  id="property_type"
                  name="property_type"
                  value={formData.property_type}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                >
                  <option value="Apartment">Apartment</option>
                  <option value="House">House</option>
                  <option value="Condo">Condo</option>
                  <option value="Villa">Villa</option>
                  <option value="Studio">Studio</option>
                </select>
              </div>
            </div>
            <div>
              <label htmlFor="address" className="block text-sm font-medium text-gray-900">
                Address
              </label>
              <input
                type="text"
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "bg-white text-gray-900"
                )}
                placeholder="Enter property address"
              />
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-900">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "bg-white text-gray-900"
                )}
                placeholder="Enter property description"
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className={cn(
                  "px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                  "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                )}
              >
                Add Property
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Add Collection Form */}
      {showCollectionForm && selectedProperty && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Add Collection to {selectedProperty.name}
          </h2>
          <form onSubmit={handleCollectionSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-900">
                  Collection Name
                </label>
                <input
                  type="text"
                  required
                  value={collectionFormData.name}
                  onChange={(e) => setCollectionFormData(prev => ({ ...prev, name: e.target.value }))}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="e.g., Kitchen Appliances, Safety Equipment"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900">
                  Budget Amount (Optional)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={collectionFormData.budget_amount}
                  onChange={(e) => setCollectionFormData(prev => ({ ...prev, budget_amount: e.target.value }))}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="0.00"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowCollectionForm(false)
                  setSelectedProperty(null)
                  setCollectionFormData({ name: '', budget_amount: '' })
                }}
                className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className={cn(
                  "px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                  "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                )}
              >
                Add Collection
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Properties List */}
      {properties.length === 0 ? (
        <div className="text-center py-12">
          <Building className="mx-auto h-12 w-12 text-gray-500" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No properties</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first property.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddForm(true)}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              )}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {properties.map((property) => (
            <div key={property.id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900">{property.name}</h3>
                  <p className="text-sm text-gray-500 capitalize">{property.property_type}</p>
                  {property.address && (
                    <div className="flex items-center mt-2">
                      <MapPin className="h-4 w-4 text-gray-500 mr-1" />
                      <p className="text-sm text-gray-500">{property.address}</p>
                    </div>
                  )}
                  {property.description && (
                    <p className="mt-2 text-sm text-gray-500">{property.description}</p>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button className="p-2 text-gray-500 hover:text-gray-900">
                    <Edit className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-gray-500 hover:text-red-600">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <p className="text-xs text-gray-500">
                  Created {new Date(property.created_at).toLocaleDateString()}
                </p>
              </div>

              {/* Collections for this property */}
              <div className="mt-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-2">Collections</h4>
                {getPropertyCollections(property.id).length === 0 ? (
                  <p className="text-sm text-gray-500">No collections for this property.</p>
                ) : (
                  <div className="space-y-2">
                    {getPropertyCollections(property.id).map((collection) => (
                      <div key={collection.id} className="flex items-center justify-between bg-gray-50 rounded-md p-3">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{collection.name}</p>
                          <p className="text-xs text-gray-500">
                            Budget: {collection.budget_amount ? `$${collection.budget_amount}` : 'Not specified'}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <button className="p-2 text-gray-500 hover:text-gray-900">
                            <Edit className="h-4 w-4" />
                          </button>
                          <button className="p-2 text-gray-500 hover:text-red-600">
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Add Collection Form */}
              {showCollectionForm && selectedProperty?.id === property.id && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-semibold text-gray-900 mb-2">Add New Collection</h4>
                  <form onSubmit={handleCollectionSubmit} className="space-y-4">
                    <div>
                      <label htmlFor="collection_name" className="block text-sm font-medium text-gray-900">
                        Collection Name
                      </label>
                      <input
                        type="text"
                        id="collection_name"
                        name="name"
                        required
                        value={collectionFormData.name}
                        onChange={(e) => setCollectionFormData({ ...collectionFormData, name: e.target.value })}
                        className={cn(
                          "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                          "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                          "bg-white text-gray-900"
                        )}
                        placeholder="Enter collection name"
                      />
                    </div>
                    <div>
                      <label htmlFor="budget_amount" className="block text-sm font-medium text-gray-900">
                        Budget Amount
                      </label>
                      <input
                        type="number"
                        id="budget_amount"
                        name="budget_amount"
                        value={collectionFormData.budget_amount}
                        onChange={(e) => setCollectionFormData({ ...collectionFormData, budget_amount: e.target.value })}
                        className={cn(
                          "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                          "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                          "bg-white text-gray-900"
                        )}
                        placeholder="Enter budget amount"
                      />
                    </div>
                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={() => setShowCollectionForm(false)}
                        className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-50"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className={cn(
                          "px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                          "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        )}
                      >
                        Add Collection
                      </button>
                    </div>
                  </form>
                </div>
              )}
              <div className="mt-2">
                <button
                  onClick={() => {
                    setSelectedProperty(property)
                    setShowCollectionForm(true)
                  }}
                  className={cn(
                    "inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md",
                    "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  )}
                >
                  <FolderPlus className="h-4 w-4 mr-2" />
                  Add Collection
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
