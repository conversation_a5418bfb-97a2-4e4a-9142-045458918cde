import { useState, useEffect } from 'react'
import { Users, Mail, UserPlus, Search } from 'lucide-react'
import { authStorage } from '../lib/auth.js'
import { executeQuery, executeUpdate } from '../lib/database.js'
import { cn } from '../lib/utils.js'
import { emailService } from '../lib/emailService.js'

const USER_ROLES = [
  { value: 'property_manager', label: 'Property Manager', description: 'Can manage properties and invite others' },
  { value: 'service_provider', label: 'Service Provider', description: 'Can view assigned tasks and update status' },
  { value: 'staff', label: 'Staff', description: 'Can view and update assigned tasks' },
  { value: 'admin', label: 'Admin', description: 'Full system access' }
]

export default function Teams() {
  const [teamMembers, setTeamMembers] = useState([])
  const [pendingInvitations, setPendingInvitations] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [showInviteForm, setShowInviteForm] = useState(false)
  const [inviteData, setInviteData] = useState({
    email: '',
    role: 'service_provider',
    full_name: '',
    message: ''
  })
  const user = authStorage.getUser()

  useEffect(() => {
    loadTeamData()
  }, [])

  const loadTeamData = async () => {
    try {
      // Load team members (users who have accepted invitations)
      const membersQuery = `
        SELECT u.*, i.invited_by_user_id, i.role as invited_role
        FROM users u
        LEFT JOIN user_invitations i ON u.email = i.email
        WHERE (i.invited_by_user_id = ? AND i.status = 'accepted') 
           OR u.id = ?
      `
      const members = executeQuery(membersQuery, [user.id, user.id])
      setTeamMembers(members)

      // Load pending invitations
      const invitationsQuery = `
        SELECT * FROM user_invitations 
        WHERE invited_by_user_id = ? AND status = 'pending'
        ORDER BY created_at DESC
      `
      const invitations = executeQuery(invitationsQuery, [user.id])
      setPendingInvitations(invitations)

    } catch (error) {
      console.error('Failed to load team data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleInviteSubmit = async (e) => {
    e.preventDefault()
    try {
      // Check if user already exists
      const existingUser = executeQuery(
        'SELECT * FROM users WHERE email = ?',
        [inviteData.email]
      )

      if (existingUser.length > 0) {
        alert('A user with this email already exists')
        return
      }

      // Check if invitation already exists
      const existingInvitation = executeQuery(
        'SELECT * FROM user_invitations WHERE email = ? AND status = ?',
        [inviteData.email, 'pending']
      )

      if (existingInvitation.length > 0) {
        alert('An invitation has already been sent to this email')
        return
      }

      // Generate invitation token
      const token = 'inv_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9)
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days

      // Create invitation
      const invitationId = 'inv_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9)
      executeUpdate(
        `INSERT INTO user_invitations (id, invited_by_user_id, email, role, full_name, message, status, token, expires_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          invitationId,
          user.id,
          inviteData.email,
          inviteData.role,
          inviteData.full_name,
          inviteData.message,
          'pending',
          token,
          expiresAt
        ]
      )

      // Send email notification
      try {
        await emailService.sendInvitationEmail(
          { ...inviteData, id: invitationId, token },
          user.full_name || user.email
        )
        console.log('Invitation email sent to:', inviteData.email)
      } catch (error) {
        console.error('Failed to send invitation email:', error)
        // Continue anyway - invitation is created
      }

      setInviteData({
        email: '',
        role: 'service_provider',
        full_name: '',
        message: ''
      })
      setShowInviteForm(false)
      loadTeamData()
    } catch (error) {
      console.error('Failed to send invitation:', error)
    }
  }

  const handleInviteChange = (e) => {
    setInviteData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin': return 'bg-purple-100 text-purple-800'
      case 'property_manager': return 'bg-blue-100 text-blue-800'
      case 'service_provider': return 'bg-green-100 text-green-800'
      case 'staff': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleLabel = (role) => {
    const roleObj = USER_ROLES.find(r => r.value === role)
    return roleObj ? roleObj.label : role
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Team Management</h1>
        <button
          onClick={() => setShowInviteForm(true)}
          className={cn(
            "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
            "text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          )}
        >
          <UserPlus className="h-4 w-4 mr-2" />
          Invite Member
        </button>
      </div>

      {/* Invite Form */}
      {showInviteForm && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Invite Team Member</h2>
          <form onSubmit={handleInviteSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  value={inviteData.email}
                  onChange={handleInviteChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label htmlFor="full_name" className="block text-sm font-medium text-gray-700">
                  Full Name
                </label>
                <input
                  type="text"
                  id="full_name"
                  name="full_name"
                  value={inviteData.full_name}
                  onChange={handleInviteChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="John Doe"
                />
              </div>
            </div>
            <div>
              <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                Role *
              </label>
              <select
                id="role"
                name="role"
                required
                value={inviteData.role}
                onChange={handleInviteChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "bg-white text-gray-900"
                )}
              >
                {USER_ROLES.map((role) => (
                  <option key={role.value} value={role.value}>
                    {role.label}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-sm text-gray-500">
                {USER_ROLES.find(r => r.value === inviteData.role)?.description}
              </p>
            </div>
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                Personal Message (Optional)
              </label>
              <textarea
                id="message"
                name="message"
                rows={3}
                value={inviteData.message}
                onChange={handleInviteChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                  "placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "bg-white text-gray-900"
                )}
                placeholder="Welcome to our team! We're excited to work with you."
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowInviteForm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className={cn(
                  "px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                  "text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                )}
              >
                <Mail className="h-4 w-4 mr-2 inline" />
                Send Invitation
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Team Members */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Team Members ({teamMembers.length})</h2>
        </div>
        {teamMembers.length === 0 ? (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No team members found</h3>
            <p className="mt-1 text-sm text-gray-500">Start by inviting your first team member</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {teamMembers.map((member) => (
              <div key={member.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {(member.full_name || member.email).charAt(0).toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">
                        {member.full_name || member.email}
                        {member.id === user.id && <span className="text-gray-500"> (You)</span>}
                      </h3>
                      <p className="text-sm text-gray-500">{member.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={cn(
                      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                      getRoleColor(member.role || member.invited_role)
                    )}>
                      {getRoleLabel(member.role || member.invited_role)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
