import { useState, useEffect } from 'react'
import { Plus, Package, Search, Edit, Trash2, AlertCircle, ExternalLink, Download, CheckSquare, Square } from 'lucide-react'
import { authStorage } from '../lib/auth.js'
import { executeQuery, executeUpdate } from '../lib/database.js'
import { cn } from '../lib/utils.js'
import { pdfService } from '../lib/pdfService.js'

export default function Inventory() {
  const [inventoryItems, setInventoryItems] = useState([])
  const [properties, setProperties] = useState([])
  const [collections, setCollections] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProperty, setSelectedProperty] = useState('')
  const [selectedCollection, setSelectedCollection] = useState('')
  const [formData, setFormData] = useState({
    property_id: '',
    collection_id: '',
    name: '',
    description: '',
    quantity_on_hand: 0,
    reorder_level: '',
    supplier_info: '',
    unit_price: '',
    amazon_product_link: ''
  })
  const [selectedItems, setSelectedItems] = useState(new Set())
  const [bulkMode, setBulkMode] = useState(false)
  const user = authStorage.getUser()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      // Load properties
      const propertiesResult = executeQuery(
        'SELECT * FROM properties WHERE owner_user_id = ? ORDER BY name',
        [user.id]
      )
      setProperties(propertiesResult)

      // Load collections
      const collectionsResult = executeQuery(
        'SELECT c.*, p.name as property_name FROM collections c JOIN properties p ON c.property_id = p.id WHERE p.owner_user_id = ? ORDER BY p.name, c.name',
        [user.id]
      )
      setCollections(collectionsResult)

      // Load inventory items
      loadInventoryItems()
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadInventoryItems = async () => {
    try {
      let query = `
        SELECT i.*, p.name as property_name, c.name as collection_name
        FROM inventory_items i
        JOIN properties p ON i.property_id = p.id
        LEFT JOIN collections c ON i.collection_id = c.id
        WHERE p.owner_user_id = ?
      `
      const params = [user.id]

      if (selectedProperty) {
        query += ' AND i.property_id = ?'
        params.push(selectedProperty)
      }

      if (selectedCollection) {
        query += ' AND i.collection_id = ?'
        params.push(selectedCollection)
      }

      if (searchTerm) {
        query += ' AND (i.name LIKE ? OR i.description LIKE ?)'
        params.push(`%${searchTerm}%`, `%${searchTerm}%`)
      }

      query += ' ORDER BY p.name, i.name'

      const result = executeQuery(query, params)
      setInventoryItems(result)
    } catch (error) {
      console.error('Failed to load inventory items:', error)
    }
  }

  useEffect(() => {
    if (!isLoading) {
      loadInventoryItems()
    }
  }, [searchTerm, selectedProperty, selectedCollection, isLoading])

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      executeUpdate(
        `INSERT INTO inventory_items (property_id, collection_id, name, description, quantity_on_hand, reorder_level, supplier_info, unit_price, amazon_product_link)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          formData.property_id,
          formData.collection_id || null,
          formData.name,
          formData.description,
          parseInt(formData.quantity_on_hand) || 0,
          formData.reorder_level ? parseInt(formData.reorder_level) : null,
          formData.supplier_info,
          formData.unit_price ? parseFloat(formData.unit_price) : null,
          formData.amazon_product_link
        ]
      )

      setFormData({
        property_id: '',
        collection_id: '',
        name: '',
        description: '',
        quantity_on_hand: 0,
        reorder_level: '',
        supplier_info: '',
        unit_price: '',
        amazon_product_link: ''
      })
      setShowAddForm(false)
      loadInventoryItems()
    } catch (error) {
      console.error('Failed to add inventory item:', error)
    }
  }

  const handleChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const getAvailableCollections = () => {
    if (!formData.property_id) return []
    return collections.filter(c => c.property_id === formData.property_id)
  }

  const isLowStock = (item) => {
    const reorderLevel = item.reorder_level || 5
    return item.quantity_on_hand <= reorderLevel
  }

  const handleExportReport = () => {
    const propertyName = selectedProperty
      ? properties.find(p => p.id === selectedProperty)?.name || 'Selected Property'
      : 'All Properties'

    pdfService.exportInventoryReport(inventoryItems, propertyName)
  }

  const handleSelectItem = (itemId) => {
    const newSelected = new Set(selectedItems)
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId)
    } else {
      newSelected.add(itemId)
    }
    setSelectedItems(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedItems.size === inventoryItems.length) {
      setSelectedItems(new Set())
    } else {
      setSelectedItems(new Set(inventoryItems.map(item => item.id)))
    }
  }

  const handleBulkDelete = () => {
    if (selectedItems.size === 0) return

    if (confirm(`Are you sure you want to delete ${selectedItems.size} items?`)) {
      try {
        selectedItems.forEach(itemId => {
          executeUpdate('DELETE FROM inventory_items WHERE id = ?', [itemId])
        })
        setSelectedItems(new Set())
        setBulkMode(false)
        loadInventoryItems()
      } catch (error) {
        console.error('Failed to delete items:', error)
      }
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-50 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-50 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
        <div className="flex items-center space-x-3">
          {bulkMode && selectedItems.size > 0 && (
            <button
              onClick={handleBulkDelete}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md",
                "text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              )}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Selected ({selectedItems.size})
            </button>
          )}
          <button
            onClick={() => setBulkMode(!bulkMode)}
            className={cn(
              "inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md",
              bulkMode
                ? "border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100"
                : "border-gray-300 text-gray-700 bg-white hover:bg-gray-50",
              "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            )}
          >
            {bulkMode ? <Square className="h-4 w-4 mr-2" /> : <CheckSquare className="h-4 w-4 mr-2" />}
            {bulkMode ? 'Exit Bulk Mode' : 'Bulk Select'}
          </button>
          <button
            onClick={handleExportReport}
            className={cn(
              "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md",
              "text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            )}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className={cn(
              "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
              "text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            )}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Item
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-900 mb-1">
              Search Items
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={cn(
                  "pl-10 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "bg-white text-gray-900"
                )}
                placeholder="Search by name or description"
              />
            </div>
          </div>
          <div>
            <label htmlFor="property-filter" className="block text-sm font-medium text-gray-900 mb-1">
              Filter by Property
            </label>
            <select
              id="property-filter"
              value={selectedProperty}
              onChange={(e) => setSelectedProperty(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "bg-white text-gray-900"
              )}
            >
              <option value="">All Properties</option>
              {properties.map((property) => (
                <option key={property.id} value={property.id}>
                  {property.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="collection-filter" className="block text-sm font-medium text-gray-900 mb-1">
              Filter by Collection
            </label>
            <select
              id="collection-filter"
              value={selectedCollection}
              onChange={(e) => setSelectedCollection(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "bg-white text-gray-900"
              )}
            >
              <option value="">All Collections</option>
              {collections.map((collection) => (
                <option key={collection.id} value={collection.id}>
                  {collection.property_name} - {collection.name}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('')
                setSelectedProperty('')
                setSelectedCollection('')
              }}
              className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-50 w-full"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Add Item Form */}
      {showAddForm && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Add New Inventory Item</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="property_id" className="block text-sm font-medium text-gray-900">
                  Property *
                </label>
                <select
                  id="property_id"
                  name="property_id"
                  required
                  value={formData.property_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                >
                  <option value="">Select a property</option>
                  {properties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="collection_id" className="block text-sm font-medium text-gray-900">
                  Collection
                </label>
                <select
                  id="collection_id"
                  name="collection_id"
                  value={formData.collection_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                >
                  <option value="">No collection</option>
                  {getAvailableCollections().map((collection) => (
                    <option key={collection.id} value={collection.id}>
                      {collection.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-900">
                  Item Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  value={formData.name}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="Enter item name"
                />
              </div>
              <div>
                <label htmlFor="quantity_on_hand" className="block text-sm font-medium text-gray-900">
                  Quantity on Hand *
                </label>
                <input
                  type="number"
                  id="quantity_on_hand"
                  name="quantity_on_hand"
                  required
                  min="0"
                  value={formData.quantity_on_hand}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="0"
                />
              </div>
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-900">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "bg-white text-gray-900"
                )}
                placeholder="Enter item description"
              />
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <label htmlFor="reorder_level" className="block text-sm font-medium text-gray-900">
                  Reorder Level
                </label>
                <input
                  type="number"
                  id="reorder_level"
                  name="reorder_level"
                  min="0"
                  value={formData.reorder_level}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="5"
                />
              </div>
              <div>
                <label htmlFor="unit_price" className="block text-sm font-medium text-gray-900">
                  Unit Price
                </label>
                <input
                  type="number"
                  id="unit_price"
                  name="unit_price"
                  min="0"
                  step="0.01"
                  value={formData.unit_price}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="0.00"
                />
              </div>
              <div>
                <label htmlFor="supplier_info" className="block text-sm font-medium text-gray-900">
                  Supplier
                </label>
                <input
                  type="text"
                  id="supplier_info"
                  name="supplier_info"
                  value={formData.supplier_info}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900"
                  )}
                  placeholder="Supplier name"
                />
              </div>
            </div>
            <div>
              <label htmlFor="amazon_product_link" className="block text-sm font-medium text-gray-900">
                Amazon Product Link
              </label>
              <input
                type="url"
                id="amazon_product_link"
                name="amazon_product_link"
                value={formData.amazon_product_link}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "bg-white text-gray-900"
                )}
                placeholder="https://amazon.com/..."
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className={cn(
                  "px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                  "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                )}
              >
                Add Item
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Inventory Items List */}
      {inventoryItems.length === 0 ? (
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-gray-500" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No inventory items</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first inventory item.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddForm(true)}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                "text-blue-600-foreground bg-blue-600 hover:bg-blue-600/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              )}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-gray-50/50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Property / Collection
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Supplier
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-border">
                {inventoryItems.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50/25">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="flex items-center">
                            <div className="text-sm font-medium text-gray-900">{item.name}</div>
                            {isLowStock(item) && (
                              <AlertCircle className="ml-2 h-4 w-4 text-orange-500" title="Low stock" />
                            )}
                          </div>
                          {item.description && (
                            <div className="text-sm text-gray-500">{item.description}</div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{item.property_name}</div>
                      {item.collection_name && (
                        <div className="text-sm text-gray-500">{item.collection_name}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <span className={cn(
                          "font-medium",
                          isLowStock(item) ? "text-orange-600" : "text-gray-900"
                        )}>
                          {item.quantity_on_hand}
                        </span>
                        {item.reorder_level && (
                          <span className="text-gray-500 ml-1">
                            (reorder at {item.reorder_level})
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.unit_price ? `$${parseFloat(item.unit_price).toFixed(2)}` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.supplier_info || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        {item.amazon_product_link && (
                          <a
                            href={item.amazon_product_link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-600/80"
                            title="View on Amazon"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        )}
                        <button className="text-gray-500 hover:text-gray-900">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-gray-500 hover:text-red-600">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}
