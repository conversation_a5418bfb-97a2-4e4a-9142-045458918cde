import { useState, useEffect } from 'react'
import { Plus, Package, Search, Edit, Trash2, AlertCircle, ExternalLink } from 'lucide-react'
import { authStorage } from '../lib/auth.js'
import { executeQuery, executeUpdate } from '../lib/database.js'
import { cn } from '../lib/utils.js'

export default function Inventory() {
  const [inventoryItems, setInventoryItems] = useState([])
  const [properties, setProperties] = useState([])
  const [collections, setCollections] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProperty, setSelectedProperty] = useState('')
  const [selectedCollection, setSelectedCollection] = useState('')
  const [formData, setFormData] = useState({
    property_id: '',
    collection_id: '',
    name: '',
    description: '',
    quantity_on_hand: 0,
    reorder_level: '',
    supplier_info: '',
    unit_price: '',
    amazon_product_link: ''
  })
  const user = authStorage.getUser()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      // Load properties
      const propertiesResult = executeQuery(
        'SELECT * FROM properties WHERE owner_user_id = ? ORDER BY name',
        [user.id]
      )
      setProperties(propertiesResult)

      // Load collections
      const collectionsResult = executeQuery(
        'SELECT c.*, p.name as property_name FROM collections c JOIN properties p ON c.property_id = p.id WHERE p.owner_user_id = ? ORDER BY p.name, c.name',
        [user.id]
      )
      setCollections(collectionsResult)

      // Load inventory items
      loadInventoryItems()
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadInventoryItems = async () => {
    try {
      let query = `
        SELECT i.*, p.name as property_name, c.name as collection_name
        FROM inventory_items i
        JOIN properties p ON i.property_id = p.id
        LEFT JOIN collections c ON i.collection_id = c.id
        WHERE p.owner_user_id = ?
      `
      const params = [user.id]

      if (selectedProperty) {
        query += ' AND i.property_id = ?'
        params.push(selectedProperty)
      }

      if (selectedCollection) {
        query += ' AND i.collection_id = ?'
        params.push(selectedCollection)
      }

      if (searchTerm) {
        query += ' AND (i.name LIKE ? OR i.description LIKE ?)'
        params.push(`%${searchTerm}%`, `%${searchTerm}%`)
      }

      query += ' ORDER BY p.name, i.name'

      const result = executeQuery(query, params)
      setInventoryItems(result)
    } catch (error) {
      console.error('Failed to load inventory items:', error)
    }
  }

  useEffect(() => {
    if (!isLoading) {
      loadInventoryItems()
    }
  }, [searchTerm, selectedProperty, selectedCollection, isLoading])

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      executeUpdate(
        `INSERT INTO inventory_items (property_id, collection_id, name, description, quantity_on_hand, reorder_level, supplier_info, unit_price, amazon_product_link)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          formData.property_id,
          formData.collection_id || null,
          formData.name,
          formData.description,
          parseInt(formData.quantity_on_hand) || 0,
          formData.reorder_level ? parseInt(formData.reorder_level) : null,
          formData.supplier_info,
          formData.unit_price ? parseFloat(formData.unit_price) : null,
          formData.amazon_product_link
        ]
      )

      setFormData({
        property_id: '',
        collection_id: '',
        name: '',
        description: '',
        quantity_on_hand: 0,
        reorder_level: '',
        supplier_info: '',
        unit_price: '',
        amazon_product_link: ''
      })
      setShowAddForm(false)
      loadInventoryItems()
    } catch (error) {
      console.error('Failed to add inventory item:', error)
    }
  }

  const handleChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const getAvailableCollections = () => {
    if (!formData.property_id) return []
    return collections.filter(c => c.property_id === formData.property_id)
  }

  const isLowStock = (item) => {
    const reorderLevel = item.reorder_level || 5
    return item.quantity_on_hand <= reorderLevel
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-24 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-foreground">Inventory Management</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className={cn(
            "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
            "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
          )}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Item
        </button>
      </div>

      {/* Filters */}
      <div className="bg-card border border-border rounded-lg p-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-foreground mb-1">
              Search Items
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={cn(
                  "pl-10 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                  "bg-background text-foreground"
                )}
                placeholder="Search by name or description"
              />
            </div>
          </div>
          <div>
            <label htmlFor="property-filter" className="block text-sm font-medium text-foreground mb-1">
              Filter by Property
            </label>
            <select
              id="property-filter"
              value={selectedProperty}
              onChange={(e) => setSelectedProperty(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                "bg-background text-foreground"
              )}
            >
              <option value="">All Properties</option>
              {properties.map((property) => (
                <option key={property.id} value={property.id}>
                  {property.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="collection-filter" className="block text-sm font-medium text-foreground mb-1">
              Filter by Collection
            </label>
            <select
              id="collection-filter"
              value={selectedCollection}
              onChange={(e) => setSelectedCollection(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                "bg-background text-foreground"
              )}
            >
              <option value="">All Collections</option>
              {collections.map((collection) => (
                <option key={collection.id} value={collection.id}>
                  {collection.property_name} - {collection.name}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('')
                setSelectedProperty('')
                setSelectedCollection('')
              }}
              className="px-4 py-2 border border-border rounded-md text-sm font-medium text-foreground hover:bg-accent w-full"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Add Item Form */}
      {showAddForm && (
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">Add New Inventory Item</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="property_id" className="block text-sm font-medium text-foreground">
                  Property *
                </label>
                <select
                  id="property_id"
                  name="property_id"
                  required
                  value={formData.property_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                >
                  <option value="">Select a property</option>
                  {properties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="collection_id" className="block text-sm font-medium text-foreground">
                  Collection
                </label>
                <select
                  id="collection_id"
                  name="collection_id"
                  value={formData.collection_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                >
                  <option value="">No collection</option>
                  {getAvailableCollections().map((collection) => (
                    <option key={collection.id} value={collection.id}>
                      {collection.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-foreground">
                  Item Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  value={formData.name}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                  placeholder="Enter item name"
                />
              </div>
              <div>
                <label htmlFor="quantity_on_hand" className="block text-sm font-medium text-foreground">
                  Quantity on Hand *
                </label>
                <input
                  type="number"
                  id="quantity_on_hand"
                  name="quantity_on_hand"
                  required
                  min="0"
                  value={formData.quantity_on_hand}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                  placeholder="0"
                />
              </div>
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-foreground">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                  "bg-background text-foreground"
                )}
                placeholder="Enter item description"
              />
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <label htmlFor="reorder_level" className="block text-sm font-medium text-foreground">
                  Reorder Level
                </label>
                <input
                  type="number"
                  id="reorder_level"
                  name="reorder_level"
                  min="0"
                  value={formData.reorder_level}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                  placeholder="5"
                />
              </div>
              <div>
                <label htmlFor="unit_price" className="block text-sm font-medium text-foreground">
                  Unit Price
                </label>
                <input
                  type="number"
                  id="unit_price"
                  name="unit_price"
                  min="0"
                  step="0.01"
                  value={formData.unit_price}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                  placeholder="0.00"
                />
              </div>
              <div>
                <label htmlFor="supplier_info" className="block text-sm font-medium text-foreground">
                  Supplier
                </label>
                <input
                  type="text"
                  id="supplier_info"
                  name="supplier_info"
                  value={formData.supplier_info}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                  placeholder="Supplier name"
                />
              </div>
            </div>
            <div>
              <label htmlFor="amazon_product_link" className="block text-sm font-medium text-foreground">
                Amazon Product Link
              </label>
              <input
                type="url"
                id="amazon_product_link"
                name="amazon_product_link"
                value={formData.amazon_product_link}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                  "bg-background text-foreground"
                )}
                placeholder="https://amazon.com/..."
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 border border-border rounded-md text-sm font-medium text-foreground hover:bg-accent"
              >
                Cancel
              </button>
              <button
                type="submit"
                className={cn(
                  "px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                  "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
                )}
              >
                Add Item
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Inventory Items List */}
      {inventoryItems.length === 0 ? (
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-sm font-medium text-foreground">No inventory items</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            Get started by adding your first inventory item.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddForm(true)}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
              )}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-card border border-border rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-muted/50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Item
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Property / Collection
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Quantity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Supplier
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {inventoryItems.map((item) => (
                  <tr key={item.id} className="hover:bg-muted/25">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="flex items-center">
                            <div className="text-sm font-medium text-foreground">{item.name}</div>
                            {isLowStock(item) && (
                              <AlertCircle className="ml-2 h-4 w-4 text-orange-500" title="Low stock" />
                            )}
                          </div>
                          {item.description && (
                            <div className="text-sm text-muted-foreground">{item.description}</div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-foreground">{item.property_name}</div>
                      {item.collection_name && (
                        <div className="text-sm text-muted-foreground">{item.collection_name}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-foreground">
                        <span className={cn(
                          "font-medium",
                          isLowStock(item) ? "text-orange-600" : "text-foreground"
                        )}>
                          {item.quantity_on_hand}
                        </span>
                        {item.reorder_level && (
                          <span className="text-muted-foreground ml-1">
                            (reorder at {item.reorder_level})
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                      {item.unit_price ? `$${parseFloat(item.unit_price).toFixed(2)}` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                      {item.supplier_info || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        {item.amazon_product_link && (
                          <a
                            href={item.amazon_product_link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:text-primary/80"
                            title="View on Amazon"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        )}
                        <button className="text-muted-foreground hover:text-foreground">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-muted-foreground hover:text-destructive">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}
