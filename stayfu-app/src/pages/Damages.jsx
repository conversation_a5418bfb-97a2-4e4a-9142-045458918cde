import { useState, useEffect } from 'react'
import { Plus, AlertTriangle, Search, Edit, Trash2, Camera, DollarSign, User, ExternalLink } from 'lucide-react'
import { authStorage } from '../lib/auth.js'
import { executeQuery, executeUpdate } from '../lib/database.js'
import { cn } from '../lib/utils.js'

const STATUS_OPTIONS = ['Reported', 'Assessment', 'Repair_In_Progress', 'Resolved']

export default function Damages() {
  const [damageReports, setDamageReports] = useState([])
  const [properties, setProperties] = useState([])
  const [serviceProviders, setServiceProviders] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [propertyFilter, setPropertyFilter] = useState('')
  const [formData, setFormData] = useState({
    property_id: '',
    description: '',
    status: 'Reported',
    estimated_cost: '',
    booking_reference_url: '',
    assigned_to_service_provider_id: ''
  })
  const user = authStorage.getUser()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      // Load properties
      const propertiesResult = executeQuery(
        'SELECT * FROM properties WHERE owner_user_id = ? ORDER BY name',
        [user.id]
      )
      setProperties(propertiesResult)

      // Load service providers
      const serviceProvidersResult = executeQuery(
        'SELECT * FROM service_providers WHERE invited_by_user_id = ? ORDER BY name',
        [user.id]
      )
      setServiceProviders(serviceProvidersResult)

      // Load damage reports
      loadDamageReports()
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadDamageReports = async () => {
    try {
      let query = `
        SELECT d.*, p.name as property_name, sp.name as service_provider_name,
               u.full_name as reported_by_name
        FROM damage_reports d
        JOIN properties p ON d.property_id = p.id
        LEFT JOIN service_providers sp ON d.assigned_to_service_provider_id = sp.id
        LEFT JOIN users u ON d.reported_by_user_id = u.id
        WHERE p.owner_user_id = ?
      `
      const params = [user.id]

      if (propertyFilter) {
        query += ' AND d.property_id = ?'
        params.push(propertyFilter)
      }

      if (statusFilter) {
        query += ' AND d.status = ?'
        params.push(statusFilter)
      }

      if (searchTerm) {
        query += ' AND d.description LIKE ?'
        params.push(`%${searchTerm}%`)
      }

      query += ' ORDER BY d.created_at DESC'

      const result = executeQuery(query, params)
      setDamageReports(result)
    } catch (error) {
      console.error('Failed to load damage reports:', error)
    }
  }

  useEffect(() => {
    if (!isLoading) {
      loadDamageReports()
    }
  }, [searchTerm, statusFilter, propertyFilter, isLoading])

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      executeUpdate(
        `INSERT INTO damage_reports (property_id, reported_by_user_id, description, status, estimated_cost, booking_reference_url, assigned_to_service_provider_id)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          formData.property_id,
          user.id,
          formData.description,
          formData.status,
          formData.estimated_cost ? parseFloat(formData.estimated_cost) : null,
          formData.booking_reference_url || null,
          formData.assigned_to_service_provider_id || null
        ]
      )

      setFormData({
        property_id: '',
        description: '',
        status: 'Reported',
        estimated_cost: '',
        booking_reference_url: '',
        assigned_to_service_provider_id: ''
      })
      setShowAddForm(false)
      loadDamageReports()
    } catch (error) {
      console.error('Failed to add damage report:', error)
    }
  }

  const handleChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Reported': return 'text-red-600 bg-red-50 border-red-200'
      case 'Assessment': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'Repair_In_Progress': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'Resolved': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusLabel = (status) => {
    return status.replace('_', ' ')
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-foreground">Damage Reports</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className={cn(
            "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
            "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
          )}
        >
          <Plus className="h-4 w-4 mr-2" />
          Report Damage
        </button>
      </div>

      {/* Filters */}
      <div className="bg-card border border-border rounded-lg p-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-foreground mb-1">
              Search Reports
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={cn(
                  "pl-10 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                  "bg-background text-foreground"
                )}
                placeholder="Search by description"
              />
            </div>
          </div>
          <div>
            <label htmlFor="property-filter" className="block text-sm font-medium text-foreground mb-1">
              Property
            </label>
            <select
              id="property-filter"
              value={propertyFilter}
              onChange={(e) => setPropertyFilter(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                "bg-background text-foreground"
              )}
            >
              <option value="">All Properties</option>
              {properties.map((property) => (
                <option key={property.id} value={property.id}>
                  {property.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-foreground mb-1">
              Status
            </label>
            <select
              id="status-filter"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={cn(
                "block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                "bg-background text-foreground"
              )}
            >
              <option value="">All Statuses</option>
              {STATUS_OPTIONS.map((status) => (
                <option key={status} value={status}>
                  {getStatusLabel(status)}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('')
                setPropertyFilter('')
                setStatusFilter('')
              }}
              className="px-4 py-2 border border-border rounded-md text-sm font-medium text-foreground hover:bg-accent w-full"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Add Damage Report Form */}
      {showAddForm && (
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">Report New Damage</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="property_id" className="block text-sm font-medium text-foreground">
                  Property *
                </label>
                <select
                  id="property_id"
                  name="property_id"
                  required
                  value={formData.property_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                >
                  <option value="">Select a property</option>
                  {properties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-foreground">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                >
                  {STATUS_OPTIONS.map((status) => (
                    <option key={status} value={status}>
                      {getStatusLabel(status)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-foreground">
                Damage Description *
              </label>
              <textarea
                id="description"
                name="description"
                required
                rows={4}
                value={formData.description}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                  "bg-background text-foreground"
                )}
                placeholder="Describe the damage in detail..."
              />
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="estimated_cost" className="block text-sm font-medium text-foreground">
                  Estimated Cost
                </label>
                <input
                  type="number"
                  id="estimated_cost"
                  name="estimated_cost"
                  min="0"
                  step="0.01"
                  value={formData.estimated_cost}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                  placeholder="0.00"
                />
              </div>
              <div>
                <label htmlFor="assigned_to_service_provider_id" className="block text-sm font-medium text-foreground">
                  Assign to Provider
                </label>
                <select
                  id="assigned_to_service_provider_id"
                  name="assigned_to_service_provider_id"
                  value={formData.assigned_to_service_provider_id}
                  onChange={handleChange}
                  className={cn(
                    "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                    "bg-background text-foreground"
                  )}
                >
                  <option value="">Unassigned</option>
                  {serviceProviders.map((provider) => (
                    <option key={provider.id} value={provider.id}>
                      {provider.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div>
              <label htmlFor="booking_reference_url" className="block text-sm font-medium text-foreground">
                Booking Reference URL
              </label>
              <input
                type="url"
                id="booking_reference_url"
                name="booking_reference_url"
                value={formData.booking_reference_url}
                onChange={handleChange}
                className={cn(
                  "mt-1 block w-full px-3 py-2 border border-input rounded-md shadow-sm",
                  "placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
                  "bg-background text-foreground"
                )}
                placeholder="https://..."
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 border border-border rounded-md text-sm font-medium text-foreground hover:bg-accent"
              >
                Cancel
              </button>
              <button
                type="submit"
                className={cn(
                  "px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                  "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
                )}
              >
                Report Damage
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Damage Reports List */}
      {damageReports.length === 0 ? (
        <div className="text-center py-12">
          <AlertTriangle className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-sm font-medium text-foreground">No damage reports</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            Get started by reporting your first damage.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddForm(true)}
              className={cn(
                "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md",
                "text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
              )}
            >
              <Plus className="h-4 w-4 mr-2" />
              Report Damage
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {damageReports.map((report) => (
            <div key={report.id} className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                    <span className={cn(
                      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",
                      getStatusColor(report.status)
                    )}>
                      {getStatusLabel(report.status)}
                    </span>
                  </div>
                  <h3 className="mt-2 text-lg font-medium text-foreground">
                    Damage Report - {report.property_name}
                  </h3>
                  <p className="mt-2 text-sm text-foreground">{report.description}</p>

                  <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                    {report.estimated_cost && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <DollarSign className="h-4 w-4 mr-1" />
                        Estimated: ${parseFloat(report.estimated_cost).toFixed(2)}
                      </div>
                    )}
                    {report.service_provider_name && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <User className="h-4 w-4 mr-1" />
                        Assigned to: {report.service_provider_name}
                      </div>
                    )}
                    {report.booking_reference_url && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <ExternalLink className="h-4 w-4 mr-1" />
                        <a
                          href={report.booking_reference_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:text-primary/80"
                        >
                          Booking Reference
                        </a>
                      </div>
                    )}
                    <div className="text-sm text-muted-foreground">
                      Reported: {new Date(report.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button className="p-2 text-muted-foreground hover:text-foreground">
                    <Camera className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-muted-foreground hover:text-foreground">
                    <Edit className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-muted-foreground hover:text-destructive">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
