import { useState, useEffect } from 'react'
import { X, CheckCircle, AlertCircle, AlertTriangle, Info, Bell, Wifi, WifiOff } from 'lucide-react'
import { cn } from '../lib/utils.js'
import { realTimeService, NOTIFICATION_TYPES } from '../lib/realTimeService.js'

export default function NotificationCenter() {
  const [notifications, setNotifications] = useState([])
  const [isConnected, setIsConnected] = useState(realTimeService.isConnected)
  const [showAll, setShowAll] = useState(false)

  useEffect(() => {
    // Subscribe to connection status
    const unsubscribeConnection = realTimeService.subscribe('connection', (data) => {
      setIsConnected(data.status === 'connected')
    })

    // Subscribe to notifications
    const unsubscribeNotifications = realTimeService.subscribe('notifications', (data) => {
      if (data.type === 'show') {
        addNotification(data.data)
      }
    })

    // Subscribe to all real-time updates for automatic notifications
    const unsubscribeUpdates = realTimeService.subscribe('all', (data) => {
      handleRealTimeUpdate(data)
    })

    return () => {
      unsubscribeConnection()
      unsubscribeNotifications()
      unsubscribeUpdates()
    }
  }, [])

  const handleRealTimeUpdate = (update) => {
    let notification = null

    switch (update.type) {
      case 'maintenance_task_updated':
        notification = {
          type: NOTIFICATION_TYPES.INFO,
          title: 'Task Updated',
          message: `${update.data.title} status changed to ${update.data.status}`,
          duration: 4000
        }
        break

      case 'inventory_low_stock_alert':
        notification = {
          type: NOTIFICATION_TYPES.WARNING,
          title: 'Low Stock Alert',
          message: `${update.data.item} is running low (${update.data.current_stock} remaining)`,
          duration: 6000
        }
        break

      case 'booking_confirmed':
        notification = {
          type: NOTIFICATION_TYPES.SUCCESS,
          title: 'New Booking',
          message: `${update.data.guest_name} booked ${update.data.property}`,
          duration: 5000
        }
        break

      case 'damage_report_created':
        notification = {
          type: NOTIFICATION_TYPES.ERROR,
          title: 'Damage Reported',
          message: `New damage report for ${update.data.property}`,
          duration: 7000
        }
        break
    }

    if (notification) {
      addNotification({
        ...notification,
        id: Date.now() + Math.random(),
        timestamp: update.timestamp
      })
    }
  }

  const addNotification = (notification) => {
    setNotifications(prev => [notification, ...prev])

    // Auto-remove after duration
    if (notification.duration > 0) {
      setTimeout(() => {
        removeNotification(notification.id)
      }, notification.duration)
    }
  }

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const clearAllNotifications = () => {
    setNotifications([])
  }

  const getNotificationIcon = (type) => {
    switch (type) {
      case NOTIFICATION_TYPES.SUCCESS:
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case NOTIFICATION_TYPES.ERROR:
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case NOTIFICATION_TYPES.WARNING:
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case NOTIFICATION_TYPES.INFO:
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getNotificationStyles = (type) => {
    switch (type) {
      case NOTIFICATION_TYPES.SUCCESS:
        return 'border-green-200 bg-green-50'
      case NOTIFICATION_TYPES.ERROR:
        return 'border-red-200 bg-red-50'
      case NOTIFICATION_TYPES.WARNING:
        return 'border-yellow-200 bg-yellow-50'
      case NOTIFICATION_TYPES.INFO:
      default:
        return 'border-blue-200 bg-blue-50'
    }
  }

  const visibleNotifications = showAll ? notifications : notifications.slice(0, 3)

  return (
    <div className="fixed top-4 right-4 z-50 w-96 max-w-sm">
      {/* Connection Status */}
      <div className={cn(
        "mb-2 flex items-center justify-between px-3 py-2 rounded-lg text-sm",
        isConnected 
          ? "bg-green-100 text-green-800 border border-green-200" 
          : "bg-red-100 text-red-800 border border-red-200"
      )}>
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <Wifi className="h-4 w-4" />
          ) : (
            <WifiOff className="h-4 w-4" />
          )}
          <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
        </div>
        {notifications.length > 0 && (
          <div className="flex items-center space-x-2">
            <Bell className="h-4 w-4" />
            <span>{notifications.length}</span>
          </div>
        )}
      </div>

      {/* Notifications */}
      <div className="space-y-2">
        {visibleNotifications.map((notification) => (
          <div
            key={notification.id}
            className={cn(
              "border rounded-lg p-4 shadow-lg transition-all duration-300 ease-in-out",
              getNotificationStyles(notification.type)
            )}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                {getNotificationIcon(notification.type)}
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900">
                    {notification.title}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {notification.message}
                  </p>
                  <p className="text-xs text-gray-400 mt-2">
                    {new Date(notification.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </div>
              <button
                onClick={() => removeNotification(notification.id)}
                className="ml-2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Show More/Less Button */}
      {notifications.length > 3 && (
        <div className="mt-2 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="text-sm text-blue-600 hover:text-blue-500"
          >
            {showAll ? 'Show Less' : `Show ${notifications.length - 3} More`}
          </button>
        </div>
      )}

      {/* Clear All Button */}
      {notifications.length > 0 && (
        <div className="mt-2 text-center">
          <button
            onClick={clearAllNotifications}
            className="text-sm text-gray-600 hover:text-gray-500"
          >
            Clear All
          </button>
        </div>
      )}
    </div>
  )
}

// Hook for using notifications in components
export function useNotifications() {
  const showNotification = (type, title, message, duration = 5000) => {
    const notification = {
      id: Date.now() + Math.random(),
      type,
      title,
      message,
      timestamp: new Date().toISOString(),
      duration
    }
    
    realTimeService.emit('notifications', 'show', notification)
    return notification
  }

  const showSuccess = (title, message, duration) => 
    showNotification(NOTIFICATION_TYPES.SUCCESS, title, message, duration)
  
  const showError = (title, message, duration) => 
    showNotification(NOTIFICATION_TYPES.ERROR, title, message, duration)
  
  const showWarning = (title, message, duration) => 
    showNotification(NOTIFICATION_TYPES.WARNING, title, message, duration)
  
  const showInfo = (title, message, duration) => 
    showNotification(NOTIFICATION_TYPES.INFO, title, message, duration)

  return {
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
}
