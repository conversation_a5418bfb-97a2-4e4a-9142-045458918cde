import { useState, useEffect } from 'react'
import { Search, Filter, X, Calendar, DollarSign, Tag, User, Building2 } from 'lucide-react'
import { cn } from '../lib/utils.js'

export default function AdvancedSearch({ 
  onSearch, 
  onFilterChange, 
  filters = {}, 
  searchPlaceholder = "Search...",
  className = "",
  showDateRange = false,
  showPriceRange = false,
  showStatusFilter = false,
  showPriorityFilter = false,
  showPropertyFilter = false,
  properties = [],
  statusOptions = [],
  priorityOptions = []
}) {
  const [searchTerm, setSearchTerm] = useState('')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [localFilters, setLocalFilters] = useState({
    dateFrom: '',
    dateTo: '',
    priceMin: '',
    priceMax: '',
    status: '',
    priority: '',
    property: '',
    tags: [],
    ...filters
  })

  useEffect(() => {
    // Debounce search
    const timer = setTimeout(() => {
      onSearch(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm, onSearch])

  useEffect(() => {
    onFilterChange(localFilters)
  }, [localFilters, onFilterChange])

  const handleFilterChange = (key, value) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const clearAllFilters = () => {
    setSearchTerm('')
    setLocalFilters({
      dateFrom: '',
      dateTo: '',
      priceMin: '',
      priceMax: '',
      status: '',
      priority: '',
      property: '',
      tags: []
    })
  }

  const hasActiveFilters = () => {
    return searchTerm || 
           localFilters.dateFrom || 
           localFilters.dateTo || 
           localFilters.priceMin || 
           localFilters.priceMax || 
           localFilters.status || 
           localFilters.priority || 
           localFilters.property ||
           localFilters.tags.length > 0
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (searchTerm) count++
    if (localFilters.dateFrom || localFilters.dateTo) count++
    if (localFilters.priceMin || localFilters.priceMax) count++
    if (localFilters.status) count++
    if (localFilters.priority) count++
    if (localFilters.property) count++
    if (localFilters.tags.length > 0) count++
    return count
  }

  return (
    <div className={cn("bg-white border border-gray-200 rounded-lg p-4", className)}>
      {/* Main Search Bar */}
      <div className="flex items-center space-x-3">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={cn(
              "pl-10 pr-4 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
              "placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              "bg-white text-gray-900"
            )}
            placeholder={searchPlaceholder}
          />
        </div>
        
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={cn(
            "inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md",
            showAdvanced || hasActiveFilters()
              ? "border-blue-300 text-blue-700 bg-blue-50"
              : "border-gray-300 text-gray-700 bg-white hover:bg-gray-50",
            "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          )}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {getActiveFilterCount() > 0 && (
            <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-blue-600 rounded-full">
              {getActiveFilterCount()}
            </span>
          )}
        </button>

        {hasActiveFilters() && (
          <button
            onClick={clearAllFilters}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            
            {/* Date Range Filter */}
            {showDateRange && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Date Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="date"
                    value={localFilters.dateFrom}
                    onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                    className={cn(
                      "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                      "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                      "bg-white text-gray-900 text-sm"
                    )}
                  />
                  <input
                    type="date"
                    value={localFilters.dateTo}
                    onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                    className={cn(
                      "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                      "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                      "bg-white text-gray-900 text-sm"
                    )}
                  />
                </div>
              </div>
            )}

            {/* Price Range Filter */}
            {showPriceRange && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  <DollarSign className="h-4 w-4 inline mr-1" />
                  Price Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={localFilters.priceMin}
                    onChange={(e) => handleFilterChange('priceMin', e.target.value)}
                    className={cn(
                      "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                      "placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                      "bg-white text-gray-900 text-sm"
                    )}
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={localFilters.priceMax}
                    onChange={(e) => handleFilterChange('priceMax', e.target.value)}
                    className={cn(
                      "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                      "placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                      "bg-white text-gray-900 text-sm"
                    )}
                  />
                </div>
              </div>
            )}

            {/* Status Filter */}
            {showStatusFilter && statusOptions.length > 0 && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  <Tag className="h-4 w-4 inline mr-1" />
                  Status
                </label>
                <select
                  value={localFilters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className={cn(
                    "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900 text-sm"
                  )}
                >
                  <option value="">All Statuses</option>
                  {statusOptions.map((status) => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Priority Filter */}
            {showPriorityFilter && priorityOptions.length > 0 && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  <Tag className="h-4 w-4 inline mr-1" />
                  Priority
                </label>
                <select
                  value={localFilters.priority}
                  onChange={(e) => handleFilterChange('priority', e.target.value)}
                  className={cn(
                    "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900 text-sm"
                  )}
                >
                  <option value="">All Priorities</option>
                  {priorityOptions.map((priority) => (
                    <option key={priority.value} value={priority.value}>
                      {priority.label}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Property Filter */}
            {showPropertyFilter && properties.length > 0 && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  <Building2 className="h-4 w-4 inline mr-1" />
                  Property
                </label>
                <select
                  value={localFilters.property}
                  onChange={(e) => handleFilterChange('property', e.target.value)}
                  className={cn(
                    "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                    "bg-white text-gray-900 text-sm"
                  )}
                >
                  <option value="">All Properties</option>
                  {properties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>

          {/* Active Filters Summary */}
          {hasActiveFilters() && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex flex-wrap gap-2">
                {searchTerm && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                    Search: "{searchTerm}"
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-2 text-blue-600 hover:text-blue-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
                
                {localFilters.status && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                    Status: {statusOptions.find(s => s.value === localFilters.status)?.label}
                    <button
                      onClick={() => handleFilterChange('status', '')}
                      className="ml-2 text-green-600 hover:text-green-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}

                {localFilters.priority && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800">
                    Priority: {priorityOptions.find(p => p.value === localFilters.priority)?.label}
                    <button
                      onClick={() => handleFilterChange('priority', '')}
                      className="ml-2 text-yellow-600 hover:text-yellow-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}

                {localFilters.property && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800">
                    Property: {properties.find(p => p.id === localFilters.property)?.name}
                    <button
                      onClick={() => handleFilterChange('property', '')}
                      className="ml-2 text-purple-600 hover:text-purple-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
