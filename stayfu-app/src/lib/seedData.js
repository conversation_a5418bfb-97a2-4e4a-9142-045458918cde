import { executeUpdate, executeQuery, getDatabase } from './database.js'

// Create sample data for development
export function createSeedData(userId) {
  try {
    // Get database to work with our in-memory system
    const db = getDatabase()

    // Create sample properties
    const property1 = {
      id: 'prop_' + Date.now() + '_1',
      owner_user_id: userId,
      name: 'Downtown Apartment',
      address: '123 Main St, City Center',
      property_type: 'Apartment',
      description: 'Modern 2-bedroom apartment in the heart of downtown',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const property2 = {
      id: 'prop_' + Date.now() + '_2',
      owner_user_id: userId,
      name: 'Beach House',
      address: '456 Ocean Drive, Beachfront',
      property_type: 'House',
      description: 'Beautiful 3-bedroom house with ocean views',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    db.properties.push(property1, property2)

    // Create sample collections
    const collection1 = {
      id: 'coll_' + Date.now() + '_1',
      property_id: property1.id,
      name: 'Kitchen Appliances',
      budget_amount: 2000.00,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const collection2 = {
      id: 'coll_' + Date.now() + '_2',
      property_id: property1.id,
      name: 'Bathroom Supplies',
      budget_amount: 500.00,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const collection3 = {
      id: 'coll_' + Date.now() + '_3',
      property_id: property2.id,
      name: 'Living Room',
      budget_amount: 1500.00,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    db.collections.push(collection1, collection2, collection3)

    // Create sample inventory items
    const inventoryItems = [
      {
        id: 'inv_' + Date.now() + '_1',
        property_id: property1.id,
        collection_id: collection1.id,
        name: 'Coffee Maker',
        description: 'Keurig K-Cup Coffee Maker',
        quantity_on_hand: 1,
        reorder_level: 1,
        unit_price: 89.99,
        supplier_info: 'Amazon',
        amazon_product_link: 'https://amazon.com/keurig-coffee-maker',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'inv_' + Date.now() + '_2',
        property_id: property1.id,
        collection_id: collection2.id,
        name: 'Toilet Paper',
        description: '2-ply bathroom tissue',
        quantity_on_hand: 3,
        reorder_level: 5,
        unit_price: 12.99,
        supplier_info: 'Costco',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'inv_' + Date.now() + '_3',
        property_id: property2.id,
        collection_id: collection3.id,
        name: 'Beach Towels',
        description: 'Large cotton beach towels',
        quantity_on_hand: 8,
        reorder_level: 6,
        unit_price: 24.99,
        supplier_info: 'Target',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    db.inventory_items.push(...inventoryItems)

    // Create sample service providers
    const serviceProviders = [
      {
        id: 'sp_' + Date.now() + '_1',
        invited_by_user_id: userId,
        name: 'ABC Plumbing',
        contact_email: '<EMAIL>',
        phone_number: '555-0123',
        service_type: 'Plumbing',
        notes: 'Reliable 24/7 plumbing service',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'sp_' + Date.now() + '_2',
        invited_by_user_id: userId,
        name: 'Elite Cleaning',
        contact_email: '<EMAIL>',
        phone_number: '555-0456',
        service_type: 'Cleaning',
        notes: 'Professional cleaning service',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    db.service_providers.push(...serviceProviders)

    // Create sample maintenance tasks
    const maintenanceTasks = [
      {
        id: 'maint_' + Date.now() + '_1',
        property_id: property1.id,
        reported_by_user_id: userId,
        assigned_to_service_provider_id: serviceProviders[0].id,
        title: 'Fix Kitchen Faucet',
        description: 'Kitchen faucet is dripping and needs repair',
        priority: 'High',
        status: 'New',
        due_date: '2024-02-15',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'maint_' + Date.now() + '_2',
        property_id: property2.id,
        reported_by_user_id: userId,
        assigned_to_service_provider_id: serviceProviders[1].id,
        title: 'Deep Clean Before Guest Arrival',
        description: 'Complete deep cleaning before next guest check-in',
        priority: 'Medium',
        status: 'In Progress',
        due_date: '2024-02-10',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    db.maintenance_tasks.push(...maintenanceTasks)

    // Create sample damage reports
    const damageReports = [
      {
        id: 'dmg_' + Date.now() + '_1',
        property_id: property1.id,
        reported_by_user_id: userId,
        assigned_to_service_provider_id: serviceProviders[0].id,
        description: 'Broken coffee table glass top',
        status: 'Reported',
        estimated_cost: 150.00,
        booking_reference_url: 'https://airbnb.com/booking/123456',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'dmg_' + Date.now() + '_2',
        property_id: property2.id,
        reported_by_user_id: userId,
        description: 'Stained carpet in bedroom',
        status: 'Assessment',
        estimated_cost: 300.00,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    db.damage_reports.push(...damageReports)

    // Create sample purchase requests
    const purchaseRequests = [
      {
        id: 'pur_' + Date.now() + '_1',
        property_id: property1.id,
        collection_id: collection1.id,
        requested_by_user_id: userId,
        item_name: 'Replacement Coffee Table',
        quantity: 1,
        estimated_price_each: 199.99,
        notes: 'Glass top coffee table to replace damaged one',
        status: 'Requested',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'pur_' + Date.now() + '_2',
        property_id: property2.id,
        collection_id: collection3.id,
        requested_by_user_id: userId,
        item_name: 'Carpet Cleaner',
        quantity: 1,
        estimated_price_each: 89.99,
        notes: 'Portable carpet cleaner for maintenance',
        status: 'Approved',
        amazon_product_link: 'https://amazon.com/carpet-cleaner',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    db.purchase_requests.push(...purchaseRequests)

    // Save to localStorage
    localStorage.setItem('stayfu_database', JSON.stringify(db))

    console.log('Sample data created successfully')
    return true
  } catch (error) {
    console.error('Failed to create sample data:', error)
    return false
  }
}

// Check if user has any data
export function hasUserData(userId) {
  try {
    const properties = executeQuery(
      'SELECT COUNT(*) as count FROM properties WHERE owner_user_id = ?',
      [userId]
    )
    return properties[0]?.count > 0
  } catch (error) {
    console.error('Failed to check user data:', error)
    return false
  }
}
