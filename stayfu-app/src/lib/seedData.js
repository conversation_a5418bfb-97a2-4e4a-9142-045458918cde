import { executeUpdate, executeQuery } from './database.js'

// Create sample data for development
export function createSeedData(userId) {
  try {
    // Create sample properties
    const property1Result = executeUpdate(
      `INSERT INTO properties (owner_user_id, name, address, property_type, description) 
       VALUES (?, ?, ?, ?, ?)`,
      [userId, 'Downtown Apartment', '123 Main St, City Center', 'Apartment', 'Modern 2-bedroom apartment in the heart of downtown']
    )

    const property2Result = executeUpdate(
      `INSERT INTO properties (owner_user_id, name, address, property_type, description) 
       VALUES (?, ?, ?, ?, ?)`,
      [userId, 'Beach House', '456 Ocean Drive, Beachfront', 'House', 'Beautiful 3-bedroom house with ocean views']
    )

    // Create sample collections
    executeUpdate(
      `INSERT INTO collections (property_id, name, budget_amount) 
       VALUES (?, ?, ?)`,
      ['property1_id', 'Kitchen Appliances', 2000.00]
    )

    executeUpdate(
      `INSERT INTO collections (property_id, name, budget_amount) 
       VALUES (?, ?, ?)`,
      ['property1_id', 'Bathroom Supplies', 500.00]
    )

    executeUpdate(
      `INSERT INTO collections (property_id, name, budget_amount) 
       VALUES (?, ?, ?)`,
      ['property2_id', 'Living Room', 1500.00]
    )

    // Create sample inventory items
    executeUpdate(
      `INSERT INTO inventory_items (property_id, name, description, quantity_on_hand, reorder_level, unit_price, supplier_info) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      ['property1_id', 'Coffee Maker', 'Keurig K-Cup Coffee Maker', 1, 1, 89.99, 'Amazon']
    )

    executeUpdate(
      `INSERT INTO inventory_items (property_id, name, description, quantity_on_hand, reorder_level, unit_price, supplier_info) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      ['property1_id', 'Toilet Paper', '2-ply bathroom tissue', 3, 5, 12.99, 'Costco']
    )

    executeUpdate(
      `INSERT INTO inventory_items (property_id, name, description, quantity_on_hand, reorder_level, unit_price, supplier_info) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      ['property2_id', 'Beach Towels', 'Large cotton beach towels', 8, 6, 24.99, 'Target']
    )

    // Create sample service provider
    executeUpdate(
      `INSERT INTO service_providers (invited_by_user_id, name, contact_email, phone_number, service_type, notes) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [userId, 'ABC Plumbing', '<EMAIL>', '555-0123', 'Plumbing', 'Reliable 24/7 plumbing service']
    )

    executeUpdate(
      `INSERT INTO service_providers (invited_by_user_id, name, contact_email, phone_number, service_type, notes) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [userId, 'Elite Cleaning', '<EMAIL>', '555-0456', 'Cleaning', 'Professional cleaning service']
    )

    // Create sample maintenance tasks
    executeUpdate(
      `INSERT INTO maintenance_tasks (property_id, reported_by_user_id, title, description, priority, status, due_date) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      ['property1_id', userId, 'Fix Kitchen Faucet', 'Kitchen faucet is dripping and needs repair', 'High', 'New', '2024-01-15']
    )

    executeUpdate(
      `INSERT INTO maintenance_tasks (property_id, reported_by_user_id, title, description, priority, status, due_date) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      ['property2_id', userId, 'Deep Clean Before Guest Arrival', 'Complete deep cleaning before next guest check-in', 'Medium', 'In Progress', '2024-01-10']
    )

    // Create sample damage reports
    executeUpdate(
      `INSERT INTO damage_reports (property_id, reported_by_user_id, description, status, estimated_cost) 
       VALUES (?, ?, ?, ?, ?)`,
      ['property1_id', userId, 'Broken coffee table glass top', 'Reported', 150.00]
    )

    executeUpdate(
      `INSERT INTO damage_reports (property_id, reported_by_user_id, description, status, estimated_cost) 
       VALUES (?, ?, ?, ?, ?)`,
      ['property2_id', userId, 'Stained carpet in bedroom', 'Assessment', 300.00]
    )

    // Create sample purchase requests
    executeUpdate(
      `INSERT INTO purchase_requests (property_id, requested_by_user_id, item_name, quantity, estimated_price_each, notes, status) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      ['property1_id', userId, 'Replacement Coffee Table', 1, 199.99, 'Glass top coffee table to replace damaged one', 'Requested']
    )

    executeUpdate(
      `INSERT INTO purchase_requests (property_id, requested_by_user_id, item_name, quantity, estimated_price_each, notes, status, amazon_product_link) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      ['property2_id', userId, 'Carpet Cleaner', 1, 89.99, 'Portable carpet cleaner for maintenance', 'Approved', 'https://amazon.com/carpet-cleaner']
    )

    console.log('Sample data created successfully')
    return true
  } catch (error) {
    console.error('Failed to create sample data:', error)
    return false
  }
}

// Check if user has any data
export function hasUserData(userId) {
  try {
    const properties = executeQuery(
      'SELECT COUNT(*) as count FROM properties WHERE owner_user_id = ?',
      [userId]
    )
    return properties[0]?.count > 0
  } catch (error) {
    console.error('Failed to check user data:', error)
    return false
  }
}
