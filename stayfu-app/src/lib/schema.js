import { getDatabase } from './database.js';

// Database schema creation for in-memory database
export function createTables() {
  const db = getDatabase();

  // Initialize tables if they don't exist
  if (!db.users) db.users = [];
  if (!db.properties) db.properties = [];
  if (!db.collections) db.collections = [];
  if (!db.inventory_items) db.inventory_items = [];
  if (!db.service_providers) db.service_providers = [];
  if (!db.maintenance_tasks) db.maintenance_tasks = [];
  if (!db.damage_reports) db.damage_reports = [];
  if (!db.damage_report_notes) db.damage_report_notes = [];
  if (!db.purchase_requests) db.purchase_requests = [];
  if (!db.property_user_assignments) db.property_user_assignments = [];
  if (!db.user_invitations) db.user_invitations = [];
  if (!db.bookings) db.bookings = [];
  if (!db.team_members) db.team_members = [];

  console.log('Database tables initialized successfully');
}

// Create triggers for updated_at timestamps (no-op for in-memory database)
export function createTriggers() {
  // In-memory database doesn't need triggers
  // Updated timestamps are handled in the database layer
  console.log('Database triggers initialized successfully');
}
