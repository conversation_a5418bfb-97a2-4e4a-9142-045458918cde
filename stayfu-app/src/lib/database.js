// Simple in-memory database for development
// This will be replaced with Supabase for production

let database = {
  users: [],
  properties: [],
  collections: [],
  inventory_items: [],
  service_providers: [],
  maintenance_tasks: [],
  damage_reports: [],
  damage_report_notes: [],
  purchase_requests: [],
  property_user_assignments: []
};

// Generate UUID-like ID
function generateId() {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

// Initialize database
export function initDatabase() {
  // Load from localStorage if available
  const stored = localStorage.getItem('stayfu_database');
  if (stored) {
    try {
      database = JSON.parse(stored);
    } catch (error) {
      console.warn('Failed to load stored database, using fresh database');
    }
  }
  console.log('Database initialized successfully');
  return database;
}

// Save database to localStorage
function saveDatabase() {
  try {
    localStorage.setItem('stayfu_database', JSON.stringify(database));
  } catch (error) {
    console.warn('Failed to save database to localStorage');
  }
}

// Get database instance
export function getDatabase() {
  return database;
}

// Execute a query (simplified for in-memory database)
export function executeQuery(query, params = []) {
  try {
    // Simple query parser for basic SELECT operations
    const lowerQuery = query.toLowerCase().trim();

    if (lowerQuery.startsWith('select count(*)')) {
      return executeCountQuery(query, params);
    } else if (lowerQuery.startsWith('select')) {
      return executeSelectQuery(query, params);
    }

    return [];
  } catch (error) {
    console.error('Query execution failed:', error);
    throw error;
  }
}

// Execute a single row query
export function executeQuerySingle(query, params = []) {
  const results = executeQuery(query, params);
  return results.length > 0 ? results[0] : null;
}

// Execute an insert/update/delete query
export function executeUpdate(query, params = []) {
  try {
    const lowerQuery = query.toLowerCase().trim();

    if (lowerQuery.startsWith('insert')) {
      return executeInsertQuery(query, params);
    } else if (lowerQuery.startsWith('update')) {
      return executeUpdateQuery(query, params);
    } else if (lowerQuery.startsWith('delete')) {
      return executeDeleteQuery(query, params);
    }

    return { changes: 0, lastInsertRowid: null };
  } catch (error) {
    console.error('Update execution failed:', error);
    throw error;
  }
}

// Simple SELECT query execution
function executeSelectQuery(query, params) {
  const lowerQuery = query.toLowerCase();

  // Handle JOIN queries
  if (lowerQuery.includes('join')) {
    return executeJoinQuery(query, params);
  }

  // Extract table name
  const fromMatch = lowerQuery.match(/from\s+(\w+)/);
  if (!fromMatch) return [];

  const tableName = fromMatch[1];
  let data = [...(database[tableName] || [])];

  // Simple WHERE clause handling
  if (lowerQuery.includes('where')) {
    data = filterData(data, query, params);
  }

  // Simple ORDER BY handling
  if (lowerQuery.includes('order by')) {
    data = sortData(data, query);
  }

  return data;
}

// Simple COUNT query execution
function executeCountQuery(query, params) {
  const results = executeSelectQuery(query.replace(/count\(\*\)/i, '*'), params);
  return [{ count: results.length }];
}

// Simple INSERT query execution
function executeInsertQuery(query, params) {
  const insertMatch = query.match(/insert\s+into\s+(\w+)\s*\(([^)]+)\)\s*values/i);
  if (!insertMatch) throw new Error('Invalid INSERT query');

  const tableName = insertMatch[1];
  const columns = insertMatch[2].split(',').map(col => col.trim());

  if (!database[tableName]) {
    database[tableName] = [];
  }

  const newRecord = {
    id: generateId(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  columns.forEach((col, index) => {
    if (params[index] !== undefined) {
      newRecord[col] = params[index];
    }
  });

  database[tableName].push(newRecord);
  saveDatabase();

  return {
    changes: 1,
    lastInsertRowid: database[tableName].length
  };
}

// Simple UPDATE query execution
function executeUpdateQuery(query, params) {
  // This is a simplified implementation
  // In a real app, you'd want proper SQL parsing
  console.log('UPDATE query not fully implemented in demo database');
  return { changes: 0 };
}

// Simple DELETE query execution
function executeDeleteQuery(query, params) {
  // This is a simplified implementation
  console.log('DELETE query not fully implemented in demo database');
  return { changes: 0 };
}

// Simple WHERE clause filtering
function filterData(data, query, params) {
  const whereMatch = query.match(/where\s+(.+?)(?:\s+order\s+by|$)/i);
  if (!whereMatch) return data;

  const whereClause = whereMatch[1].toLowerCase();

  // Simple parameter substitution (very basic)
  if (whereClause.includes('?') && params.length > 0) {
    return data.filter(record => {
      // Handle owner_user_id filter
      if (whereClause.includes('owner_user_id = ?')) {
        return record.owner_user_id === params[0];
      }

      // Handle email filter for login
      if (whereClause.includes('email = ?')) {
        return record.email === params[0];
      }

      // Handle id filter
      if (whereClause.includes('id = ?')) {
        return record.id === params[0];
      }

      // Handle property_id filter
      if (whereClause.includes('property_id = ?')) {
        return record.property_id === params[0];
      }

      // Handle status filter
      if (whereClause.includes('status = ?')) {
        return record.status === params[0];
      }

      // Handle priority filter
      if (whereClause.includes('priority = ?')) {
        return record.priority === params[0];
      }

      return true;
    });
  }

  return data;
}

// Simple ORDER BY sorting
function sortData(data, query) {
  const orderMatch = query.match(/order\s+by\s+(\w+\.)?(\w+)(?:\s+(asc|desc))?/i);
  if (!orderMatch) return data;

  const column = orderMatch[2]; // Get column name without table prefix
  const direction = (orderMatch[3] || 'asc').toLowerCase();

  return [...data].sort((a, b) => {
    const aVal = a[column];
    const bVal = b[column];

    if (direction === 'desc') {
      return bVal > aVal ? 1 : -1;
    }
    return aVal > bVal ? 1 : -1;
  });
}

// Handle JOIN queries (simplified)
function executeJoinQuery(query, params) {
  const lowerQuery = query.toLowerCase();

  // Handle inventory items with property and collection names
  if (lowerQuery.includes('inventory_items') && lowerQuery.includes('properties')) {
    const inventoryItems = database.inventory_items || [];
    const properties = database.properties || [];
    const collections = database.collections || [];

    let result = inventoryItems.map(item => {
      const property = properties.find(p => p.id === item.property_id);
      const collection = collections.find(c => c.id === item.collection_id);

      return {
        ...item,
        property_name: property?.name || '',
        collection_name: collection?.name || ''
      };
    });

    // Apply WHERE filters
    if (lowerQuery.includes('where')) {
      result = filterJoinedData(result, query, params);
    }

    // Apply ORDER BY
    if (lowerQuery.includes('order by')) {
      result = sortData(result, query);
    }

    return result;
  }

  // Handle maintenance tasks with property and service provider names
  if (lowerQuery.includes('maintenance_tasks') && lowerQuery.includes('properties')) {
    const tasks = database.maintenance_tasks || [];
    const properties = database.properties || [];
    const serviceProviders = database.service_providers || [];
    const users = database.users || [];

    let result = tasks.map(task => {
      const property = properties.find(p => p.id === task.property_id);
      const serviceProvider = serviceProviders.find(sp => sp.id === task.assigned_to_service_provider_id);
      const reportedBy = users.find(u => u.id === task.reported_by_user_id);

      return {
        ...task,
        property_name: property?.name || '',
        service_provider_name: serviceProvider?.name || '',
        reported_by_name: reportedBy?.full_name || reportedBy?.email || ''
      };
    });

    // Apply WHERE filters
    if (lowerQuery.includes('where')) {
      result = filterJoinedData(result, query, params);
    }

    // Apply ORDER BY
    if (lowerQuery.includes('order by')) {
      result = sortData(result, query);
    }

    return result;
  }

  // Handle damage reports with property and service provider names
  if (lowerQuery.includes('damage_reports') && lowerQuery.includes('properties')) {
    const reports = database.damage_reports || [];
    const properties = database.properties || [];
    const serviceProviders = database.service_providers || [];
    const users = database.users || [];

    let result = reports.map(report => {
      const property = properties.find(p => p.id === report.property_id);
      const serviceProvider = serviceProviders.find(sp => sp.id === report.assigned_to_service_provider_id);
      const reportedBy = users.find(u => u.id === report.reported_by_user_id);

      return {
        ...report,
        property_name: property?.name || '',
        service_provider_name: serviceProvider?.name || '',
        reported_by_name: reportedBy?.full_name || reportedBy?.email || ''
      };
    });

    // Apply WHERE filters
    if (lowerQuery.includes('where')) {
      result = filterJoinedData(result, query, params);
    }

    // Apply ORDER BY
    if (lowerQuery.includes('order by')) {
      result = sortData(result, query);
    }

    return result;
  }

  // Handle purchase requests with property and collection names
  if (lowerQuery.includes('purchase_requests') && lowerQuery.includes('properties')) {
    const requests = database.purchase_requests || [];
    const properties = database.properties || [];
    const collections = database.collections || [];
    const users = database.users || [];

    let result = requests.map(request => {
      const property = properties.find(p => p.id === request.property_id);
      const collection = collections.find(c => c.id === request.collection_id);
      const requestedBy = users.find(u => u.id === request.requested_by_user_id);

      return {
        ...request,
        property_name: property?.name || '',
        collection_name: collection?.name || '',
        requested_by_name: requestedBy?.full_name || requestedBy?.email || ''
      };
    });

    // Apply WHERE filters
    if (lowerQuery.includes('where')) {
      result = filterJoinedData(result, query, params);
    }

    // Apply ORDER BY
    if (lowerQuery.includes('order by')) {
      result = sortData(result, query);
    }

    return result;
  }

  // Handle collections with property names
  if (lowerQuery.includes('collections') && lowerQuery.includes('properties')) {
    const collections = database.collections || [];
    const properties = database.properties || [];

    let result = collections.map(collection => {
      const property = properties.find(p => p.id === collection.property_id);

      return {
        ...collection,
        property_name: property?.name || ''
      };
    });

    // Apply WHERE filters
    if (lowerQuery.includes('where')) {
      result = filterJoinedData(result, query, params);
    }

    // Apply ORDER BY
    if (lowerQuery.includes('order by')) {
      result = sortData(result, query);
    }

    return result;
  }

  // Fallback to simple query
  return executeSelectQuery(query.replace(/join.*?on.*?=/gi, ''), params);
}

// Filter joined data
function filterJoinedData(data, query, params) {
  const whereMatch = query.match(/where\s+(.+?)(?:\s+order\s+by|$)/i);
  if (!whereMatch || params.length === 0) return data;

  const whereClause = whereMatch[1].toLowerCase();

  return data.filter(record => {
    // Handle owner_user_id filter
    if (whereClause.includes('owner_user_id') && params[0]) {
      return record.owner_user_id === params[0];
    }

    // Handle property_id filter
    if (whereClause.includes('property_id') && params.length > 0) {
      const paramIndex = whereClause.includes('owner_user_id') ? 1 : 0;
      return record.property_id === params[paramIndex];
    }

    // Handle status filter
    if (whereClause.includes('status') && params.length > 0) {
      const paramIndex = whereClause.split('?').length - 2; // Get last parameter
      return record.status === params[paramIndex];
    }

    // Handle priority filter
    if (whereClause.includes('priority') && params.length > 0) {
      const paramIndex = whereClause.split('?').length - 2;
      return record.priority === params[paramIndex];
    }

    // Handle LIKE searches
    if (whereClause.includes('like') && params.length > 0) {
      const searchTerm = params[params.length - 1];
      if (searchTerm && searchTerm.includes('%')) {
        const term = searchTerm.replace(/%/g, '').toLowerCase();
        return (record.name && record.name.toLowerCase().includes(term)) ||
               (record.title && record.title.toLowerCase().includes(term)) ||
               (record.description && record.description.toLowerCase().includes(term)) ||
               (record.item_name && record.item_name.toLowerCase().includes(term));
      }
    }

    return true;
  });
}
