// Real-time updates service
// In production, this would use WebSockets, Server-Sent Events, or a service like Pusher

class RealTimeService {
  constructor() {
    this.subscribers = new Map()
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    
    // Simulate connection
    this.connect()
  }

  connect() {
    // Simulate WebSocket connection
    console.log('🔌 Connecting to real-time service...')
    
    setTimeout(() => {
      this.isConnected = true
      this.reconnectAttempts = 0
      console.log('✅ Connected to real-time service')
      
      // Notify all subscribers about connection
      this.notifySubscribers('connection', { status: 'connected' })
      
      // Start heartbeat
      this.startHeartbeat()
    }, 1000)
  }

  disconnect() {
    this.isConnected = false
    console.log('❌ Disconnected from real-time service')
    this.notifySubscribers('connection', { status: 'disconnected' })
  }

  startHeartbeat() {
    setInterval(() => {
      if (this.isConnected) {
        // Simulate periodic updates
        this.simulateUpdates()
      }
    }, 30000) // Every 30 seconds
  }

  simulateUpdates() {
    // Simulate random updates for demo purposes
    const updateTypes = [
      'maintenance_task_updated',
      'damage_report_created',
      'inventory_low_stock',
      'booking_confirmed',
      'service_provider_assigned'
    ]
    
    const randomType = updateTypes[Math.floor(Math.random() * updateTypes.length)]
    
    switch (randomType) {
      case 'maintenance_task_updated':
        this.notifySubscribers('maintenance', {
          type: 'task_updated',
          data: {
            id: 'task_' + Date.now(),
            title: 'Simulated task update',
            status: 'In Progress',
            timestamp: new Date().toISOString()
          }
        })
        break
        
      case 'inventory_low_stock':
        this.notifySubscribers('inventory', {
          type: 'low_stock_alert',
          data: {
            item: 'Toilet Paper',
            current_stock: 2,
            reorder_level: 5,
            timestamp: new Date().toISOString()
          }
        })
        break
        
      case 'booking_confirmed':
        this.notifySubscribers('bookings', {
          type: 'booking_confirmed',
          data: {
            guest_name: 'John Doe',
            check_in: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            property: 'Downtown Apartment',
            timestamp: new Date().toISOString()
          }
        })
        break
    }
  }

  subscribe(channel, callback) {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, new Set())
    }
    
    this.subscribers.get(channel).add(callback)
    
    // Return unsubscribe function
    return () => {
      const channelSubscribers = this.subscribers.get(channel)
      if (channelSubscribers) {
        channelSubscribers.delete(callback)
        if (channelSubscribers.size === 0) {
          this.subscribers.delete(channel)
        }
      }
    }
  }

  unsubscribe(channel, callback) {
    const channelSubscribers = this.subscribers.get(channel)
    if (channelSubscribers) {
      channelSubscribers.delete(callback)
      if (channelSubscribers.size === 0) {
        this.subscribers.delete(channel)
      }
    }
  }

  notifySubscribers(channel, data) {
    const channelSubscribers = this.subscribers.get(channel)
    if (channelSubscribers) {
      channelSubscribers.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Error in real-time subscriber:', error)
        }
      })
    }
  }

  // Emit custom events
  emit(channel, eventType, data) {
    this.notifySubscribers(channel, {
      type: eventType,
      data,
      timestamp: new Date().toISOString()
    })
  }

  // Send updates when data changes
  broadcastUpdate(entityType, action, data) {
    const updateData = {
      type: `${entityType}_${action}`,
      data,
      timestamp: new Date().toISOString()
    }

    // Broadcast to relevant channels
    this.notifySubscribers(entityType, updateData)
    this.notifySubscribers('all', updateData)
    
    console.log(`📡 Broadcasting ${entityType}_${action}:`, data)
  }

  // Get connection status
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    }
  }

  // Force reconnection
  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`🔄 Reconnecting... (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('❌ Max reconnection attempts reached')
    }
  }
}

// Create singleton instance
export const realTimeService = new RealTimeService()

// React hook for using real-time updates (to be used in components)
// Note: This requires React to be imported in the component using it
export function createRealTimeHook() {
  return function useRealTimeUpdates(channel, callback) {
    // This will be implemented in components that import React
    console.log('Real-time hook created for channel:', channel)
  }
}

// Notification types
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
}

// Helper function to show notifications
export function showNotification(type, title, message, duration = 5000) {
  const notification = {
    id: Date.now() + Math.random(),
    type,
    title,
    message,
    timestamp: new Date().toISOString(),
    duration
  }
  
  realTimeService.emit('notifications', 'show', notification)
  
  return notification
}

export default realTimeService
