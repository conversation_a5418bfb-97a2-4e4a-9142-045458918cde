// background.js (Refactored for Manifest V3) - Version 2.0.2
console.log('StayFu Extension Background Script Loaded - Version 2.0.2');

// --- Configuration & State ---
let stayfuAppUrl = 'http://localhost:8080'; // Default, will be loaded from storage
let stayfuApiToken = null; // Will be loaded from storage
let searchResults = []; // In-memory cache of current search results
let isSearching = false; // Flag to track if a search is in progress
let currentSearchTerm = ''; // Current search term

// --- Helper Functions ---
function formatBaseUrl(url) {
    if (!url) return null;

    try {
        // If the URL doesn't start with a protocol, add https:// by default
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            // Prefer https for production domains
            if (url.includes('stayfu.com') || url.includes('stayfuse.com')) {
                url = `https://${url}`;
            } else {
                url = `http://${url}`; // Use http for localhost
            }
        }

        // Try to construct a URL object to validate
        const urlObj = new URL(url);

        // Special handling for stayfu.com and stayfuse.com domains
        const hostname = urlObj.hostname.toLowerCase();

        // Check for common domain typos and fix them
        if (hostname === 'stayfuse.com' || hostname.endsWith('.stayfuse.com')) {
            console.log('BG: Correcting stayfuse.com to stayfu.com');
            // Replace stayfuse.com with stayfu.com
            urlObj.hostname = urlObj.hostname.replace('stayfuse.com', 'stayfu.com');
        }

        // Return the formatted URL including protocol, hostname, and port if present
        return urlObj.toString().replace(/\/$/, ''); // Remove trailing slash if present
    } catch (error) {
        console.error('BG: Invalid URL format:', error);
        return null;
    }
}

function formatProductData(product) {
    return {
        name: product.title || 'N/A',
        sku: product.asin || `UNKNOWN_${Date.now()}`,
        image_url: product.img || '',
        price: parseFloat(product.numericPrice) || 0,
        supplier: 'Amazon',
        supplier_url: product.url || `https://www.amazon.com/dp/${product.asin}`,
        features: product.details?.bullet_points || [],
        specifications: product.details?.specifications || {},
        external_id: product.asin,
        // Add a flag to indicate if the image is a processed data URL
        has_processed_image: !!product.hasProcessedImage,
        metadata: {
            rating: product.rating,
            reviewCount: product.reviewCount,
            isPrime: product.isPrime,
            availability: product.availability,
            searchTerm: product.searchTerm
        }
    };
}

// --- Initialization ---
async function loadConfig() {
    try {
        const result = await chrome.storage.local.get(['stayfuAppUrl', 'stayfuToken']);

        // Ensure URL has proper protocol and format
        if (result.stayfuAppUrl) {
            const formattedUrl = formatBaseUrl(result.stayfuAppUrl);
            if (formattedUrl) {
                stayfuAppUrl = formattedUrl;
            } else {
                stayfuAppUrl = 'http://localhost:8080'; // Default if invalid
                console.error('BG: Invalid URL in storage, using default');
            }
        } else {
            stayfuAppUrl = 'http://localhost:8080'; // Default
        }

        // Validate token
        stayfuApiToken = result.stayfuToken || null;
        if (stayfuApiToken) {
            if (typeof stayfuApiToken !== 'string' || stayfuApiToken.trim() === '') {
                console.error('BG: Invalid token format in storage');
                stayfuApiToken = null;
                // Clean up invalid token
                await chrome.storage.local.remove(['stayfuToken', 'stayfuTokenHash']);
            }
        }

        console.log('BG: StayFu config loaded:', {
            url: stayfuAppUrl,
            hasToken: !!stayfuApiToken,
            tokenPrefix: stayfuApiToken ? stayfuApiToken.substring(0, 10) + '...' : 'none'
        });
    } catch (error) {
        console.error("BG: Error loading config:", error);
        stayfuApiToken = null; // Reset token on error
        stayfuAppUrl = 'http://localhost:8080'; // Reset to default URL
    }
}

// Handle getting StayFu settings
async function handleGetStayfuSettings(_request, sendResponse) {
    try {
        sendResponse({
            url: stayfuAppUrl,
            hasToken: !!stayfuApiToken
        });
    } catch (error) {
        console.error('BG: Error getting settings:', error);
        sendResponse({ error: error.message });
    }
}

// Handle checking StayFu connection
async function handleCheckStayfuConnection(_request, sendResponse) {
  try {
    // Try multiple API endpoint patterns
    const possibleEndpoints = [
      `/api/extension-status`,
      `/api/v1/extension-status`,
      `/extension-status`
    ];

    // Try a simple ping first to check if the domain is reachable
    try {
      console.log(`BG: Pinging domain: ${stayfuAppUrl}`);
      const pingResponse = await fetch(stayfuAppUrl, {
        method: 'HEAD',
        mode: 'no-cors' // This allows the request to succeed even with CORS restrictions
      });

      if (!pingResponse) {
        throw new Error('Domain ping failed');
      }

      console.log('BG: Domain is reachable, checking API endpoints');
    } catch (pingError) {
      console.error('BG: Domain ping failed:', pingError);
      sendResponse({
        connected: false,
        error: 'Cannot connect to domain. Please check the URL and your internet connection.'
      });
      return;
    }

    // Try each API endpoint
    for (const endpoint of possibleEndpoints) {
      const statusUrl = `${stayfuAppUrl}${endpoint}`;
      console.log(`BG: Checking API endpoint: ${statusUrl}`);

      // Make the request with a timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout per endpoint

      try {
        const response = await fetch(statusUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        // If we got a response (even if not 200), the endpoint exists
        console.log(`BG: Got response from ${endpoint}, status: ${response.status}`);

        if (response.ok) {
          // Check the content type to handle non-JSON responses
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('text/html')) {
            console.log('BG: Received HTML response instead of JSON');
            continue; // Try the next endpoint
          }

          // Try to parse the response as JSON
          try {
            const data = await response.json();
            console.log('BG: Connection check response:', data);

            // If we got a valid JSON response with the expected format, we're done
            if (data && data.status === 'available') {
              sendResponse({
                connected: true,
                message: data.message || 'Connected successfully'
              });
              return;
            }
          } catch (jsonError) {
            console.error('BG: Failed to parse JSON response:', jsonError);
            // Continue to the next endpoint
          }
        }
      } catch (fetchError) {
        clearTimeout(timeoutId);
        console.error(`BG: Error checking endpoint ${endpoint}:`, fetchError);
        // Continue to the next endpoint
      }
    }

    // If we get here, none of the API endpoints worked, but the domain is reachable
    console.log('BG: Domain is reachable, but no API endpoints responded correctly');
    sendResponse({
      connected: true,
      message: 'Connected to website, but API endpoints may not be accessible'
    });

  } catch (error) {
    console.error('BG: Error checking connection:', error);
    sendResponse({
      connected: false,
      error: error.message || 'Connection check failed'
    });
  }
}

// Handle setting StayFu URL
async function handleSetStayfuUrl(request, sendResponse) {
    const url = request.url;
    console.log('BG: Setting StayFu URL:', url);

    try {
        const formattedUrl = formatBaseUrl(url);
        if (!formattedUrl) {
            throw new Error('Invalid URL format');
        }

        await chrome.storage.local.set({ stayfuAppUrl: formattedUrl });
        stayfuAppUrl = formattedUrl;
        sendResponse({ success: true });
    } catch (error) {
        console.error('BG: Error setting StayFu URL:', error);
        sendResponse({
            success: false,
            error: error.message || 'Failed to save URL'
        });
    }
}

// Handle setting StayFu token
async function handleSetStayfuToken(request, sendResponse) {
    const token = request.token;
    console.log('BG: Setting StayFu token');

    try {
        if (!token || typeof token !== 'string' || token.trim() === '') {
            throw new Error('Invalid token format');
        }

        // Validate token with the API
        const validationUrl = `${stayfuAppUrl}/api/extension/validate-token`;
        console.log('BG: Validating token with:', validationUrl);

        const response = await fetch(validationUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ token })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || `Token validation failed: ${response.status}`);
        }

        const data = await response.json();
        if (!data.valid) {
            throw new Error(data.message || 'Token is not valid');
        }

        // Save the token
        await chrome.storage.local.set({ stayfuToken: token });
        stayfuApiToken = token;

        sendResponse({
            success: true,
            message: 'Token validated and saved successfully'
        });
    } catch (error) {
        console.error('BG: Error setting StayFu token:', error);
        sendResponse({
            success: false,
            error: error.message || 'Failed to validate token'
        });
    }
}

// Handle product data received from content script
function handleProductData(request, _sender, sendResponse) {
    console.log(`BG: Received product data for "${request.searchTerm}":`,
               `${request.products.length} products`);

    // Store the search results
    searchResults = request.products;

    // Send the results back to the app
    sendSearchResultsToApp(request.searchTerm, request.products);

    // No response needed
    if (sendResponse) {
        sendResponse({ success: true });
    }
}

// Send search results back to the app
async function sendSearchResultsToApp(searchTerm, products) {
    try {
        console.log(`BG: Sending search results to app: ${products.length} products for "${searchTerm}"`);

        // Method 1: Try to find the app tab and use executeScript to post a message
        const tabs = await chrome.tabs.query({ url: [
            `${stayfuAppUrl}/*`,
            'http://localhost:*/*',
            'http://127.0.0.1:*/*',
            'https://*.stayfu.com/*',
            'https://stayfu.com/*',
            'https://stage.stayfu.com/*'
        ]});

        // Method 2: Use external messaging to send results directly to the app
        // This is more reliable than the window.postMessage approach
        try {
            // Find all tabs that might contain our app
            if (tabs.length > 0) {
                console.log(`BG: Found ${tabs.length} potential app tabs`);

                // Try each tab in sequence
                for (const tab of tabs) {
                    try {
                        // Try to send a message to the app in this tab
                        console.log(`BG: Trying to send search results to app in tab ${tab.id} (URL: ${tab.url})`);

                        // First try direct external messaging to the app
                        const appOrigin = new URL(tab.url).origin;
                        console.log(`BG: Sending external message to ${appOrigin}`);

                        // Use content script to post a message to the window
                        const sendMessagePromise = new Promise((resolve) => {
                            chrome.tabs.sendMessage(tab.id, {
                                type: 'STAYFU_SEARCH_RESULTS',
                                searchTerm,
                                products
                            }, (response) => {
                                if (chrome.runtime.lastError) {
                                    console.warn(`BG: Error sending message to tab ${tab.id}:`, chrome.runtime.lastError);

                                    // Fallback to injecting script
                                    try {
                                        chrome.scripting.executeScript({
                                            target: { tabId: tab.id },
                                            func: (searchTermArg, productsArg) => {
                                                console.log(`Injected script posting message with ${productsArg.length} products`);

                                                // Store the results in localStorage as a backup
                                                try {
                                                    const resultsData = {
                                                        searchTerm: searchTermArg,
                                                        products: productsArg,
                                                        timestamp: Date.now()
                                                    };
                                                    localStorage.setItem('stayfuLastSearchResults', JSON.stringify(resultsData));
                                                    console.log("Injected script stored search results in localStorage");
                                                } catch (storageError) {
                                                    console.error("Injected script ERROR storing search results in localStorage:", storageError);
                                                }

                                                // Post the message to the window
                                                window.postMessage({
                                                    type: 'STAYFU_SEARCH_RESULTS',
                                                    searchTerm: searchTermArg,
                                                    products: productsArg
                                                }, window.location.origin);
                                            },
                                            args: [searchTerm, products]
                                        }, (results) => {
                                            if (chrome.runtime.lastError) {
                                                console.error(`BG: Error executing script in tab ${tab.id}:`, chrome.runtime.lastError);
                                            } else {
                                                console.log(`BG: Script executed successfully in tab ${tab.id}:`, results);
                                            }
                                            resolve();
                                        });
                                    } catch (scriptError) {
                                        console.error(`BG: Error executing script in tab ${tab.id}:`, scriptError);
                                        resolve();
                                    }
                                } else {
                                    console.log(`BG: Successfully sent search results to tab ${tab.id}`, response);
                                    resolve();
                                }
                            });
                        });

                        // Set a timeout for the sendMessage operation
                        const timeoutPromise = new Promise((resolve) => {
                            setTimeout(() => {
                                console.warn(`BG: Timeout sending message to tab ${tab.id}`);
                                resolve();
                            }, 2000); // 2 second timeout
                        });

                        // Wait for either the sendMessage to complete or the timeout
                        await Promise.race([sendMessagePromise, timeoutPromise]);

                    } catch (tabError) {
                        console.error(`BG: Error processing tab ${tab.id}:`, tabError);
                    }
                }
            } else {
                console.warn('BG: No StayFu app tabs found to send results to');

                // If no tabs are found, try to open a new tab with the app
                try {
                    console.log(`BG: Attempting to open app in new tab: ${stayfuAppUrl}`);
                    const newTab = await chrome.tabs.create({
                        url: `${stayfuAppUrl}/#/inventory/amazon-search`,
                        active: true
                    });

                    // Wait for the tab to load
                    console.log(`BG: Waiting for new tab ${newTab.id} to load`);

                    // Set up a listener for when the tab is updated
                    chrome.tabs.onUpdated.addListener(function listener(tabId, changeInfo, _tab) {
                        if (tabId === newTab.id && changeInfo.status === 'complete') {
                            // Remove the listener to avoid multiple calls
                            chrome.tabs.onUpdated.removeListener(listener);

                            // Wait a bit for the app to initialize
                            setTimeout(() => {
                                // Try to send the results to the new tab
                                chrome.tabs.sendMessage(newTab.id, {
                                    type: 'STAYFU_SEARCH_RESULTS',
                                    searchTerm,
                                    products
                                }, (response) => {
                                    if (chrome.runtime.lastError) {
                                        console.warn(`BG: Error sending message to new tab ${newTab.id}:`, chrome.runtime.lastError);

                                        // Fallback to injecting script
                                        try {
                                            chrome.scripting.executeScript({
                                                target: { tabId: newTab.id },
                                                func: (searchTermArg, productsArg) => {
                                                    console.log(`Injected script posting message with ${productsArg.length} products`);

                                                    // Store the results in localStorage as a backup
                                                    try {
                                                        const resultsData = {
                                                            searchTerm: searchTermArg,
                                                            products: productsArg,
                                                            timestamp: Date.now()
                                                        };
                                                        localStorage.setItem('stayfuLastSearchResults', JSON.stringify(resultsData));
                                                        console.log("Injected script stored search results in localStorage");
                                                    } catch (storageError) {
                                                        console.error("Injected script ERROR storing search results in localStorage:", storageError);
                                                    }

                                                    // Post the message to the window
                                                    window.postMessage({
                                                        type: 'STAYFU_SEARCH_RESULTS',
                                                        searchTerm: searchTermArg,
                                                        products: productsArg
                                                    }, window.location.origin);
                                                },
                                                args: [searchTerm, products]
                                            });
                                        } catch (scriptError) {
                                            console.error(`BG: Error executing script in new tab ${newTab.id}:`, scriptError);
                                        }
                                    } else {
                                        console.log(`BG: Successfully sent search results to new tab ${newTab.id}`, response);
                                    }
                                });
                            }, 2000); // Wait 2 seconds for the app to initialize
                        }
                    });
                } catch (newTabError) {
                    console.error('BG: Error opening new tab:', newTabError);
                }
            }
        } catch (error) {
            console.error('BG: Error sending results via tabs:', error);
        }

        // Update search state
        isSearching = false;

    } catch (error) {
        console.error('BG: Error sending search results to app:', error);
        isSearching = false;
    }
}

// Handle starting a search
async function handleStartSearch(request, sendResponse) {
    try {
        const searchTerm = request.searchTerm;
        if (!searchTerm) {
            throw new Error('No search term provided');
        }

        console.log(`BG: Starting search for "${searchTerm}"`);

        // Update state
        isSearching = true;
        currentSearchTerm = searchTerm;
        searchResults = [];

        // Create a search URL
        const searchUrl = `https://www.amazon.com/s?k=${encodeURIComponent(searchTerm)}`;

        // Open the search in a new tab
        const newTab = await chrome.tabs.create({ url: searchUrl, active: false });

        // Tell the content script to scrape this page when it loads
        chrome.tabs.onUpdated.addListener(function listener(tabId, changeInfo, _tab) {
            if (tabId === newTab.id && changeInfo.status === 'complete') {
                // Remove the listener to avoid multiple calls
                chrome.tabs.onUpdated.removeListener(listener);

                // Send message to content script to scrape the page
                chrome.tabs.sendMessage(tabId, {
                    action: 'scrapeSearchPage',
                    searchTerm
                });
            }
        });

        sendResponse({
            success: true,
            message: `Search initiated for "${searchTerm}"`
        });
    } catch (error) {
        console.error('BG: Error starting search:', error);
        isSearching = false;
        sendResponse({
            success: false,
            error: error.message || 'Failed to start search'
        });
    }
}

// Handle getting search status
function handleGetSearchStatus(_request, sendResponse) {
    sendResponse({
        isSearching,
        currentSearchTerm,
        resultsCount: searchResults.length
    });
}

// Handle getting search results
function handleGetSearchResults(_request, sendResponse) {
    sendResponse({
        success: true,
        searchTerm: currentSearchTerm,
        products: searchResults
    });
}

// Handle updating property data
function handleUpdatePropertyData(request, sendResponse) {
    console.log('BG: Handling updatePropertyData', request.propertyData);

    // Store the property data in local storage for later use
    chrome.storage.local.set({
        propertyData: request.propertyData
    }, () => {
        if (chrome.runtime.lastError) {
            console.error('BG: Error storing property data:', chrome.runtime.lastError);
            sendResponse({ success: false, error: chrome.runtime.lastError.message });
        } else {
            console.log('BG: Property data stored successfully');
            sendResponse({ success: true });
        }
    });
}

// --- External Message Handling ---
// Handle external connection messages from web pages/apps
chrome.runtime.onMessageExternal.addListener(
  (request, sender, sendResponse) => {
    console.log('BG: Received external message:', request, 'from:', sender.url);
    const originUrl = sender.url ? new URL(sender.url).origin : null;

    // Check if the sender is from a trusted origin
    if (!originUrl) {
      console.error('BG: Rejected message from unknown origin');
      sendResponse({ success: false, error: 'Unknown origin' });
      return;
    }

    // Check if the origin matches our app URL or localhost
    const isLocalhost = originUrl.startsWith('http://localhost:') || originUrl.startsWith('http://127.0.0.1:');

    // More flexible domain checking for production domains
    const isStayfuDomain = originUrl.includes('stayfu.com') || originUrl.includes('stayfuse.com');
    const isStayfuApp = originUrl === stayfuAppUrl || isStayfuDomain;

    if (!isLocalhost && !isStayfuApp) {
      console.error(`BG: Rejected message from untrusted origin: ${originUrl}`);
      sendResponse({ success: false, error: 'Untrusted origin' });
      return;
    }

    // Log the trusted origin
    console.log(`BG: Accepted message from trusted origin: ${originUrl} (isLocalhost: ${isLocalhost}, isStayfuDomain: ${isStayfuDomain})`);

    console.log(`BG: Processing external message from trusted origin: ${originUrl}`);

    // Handle different message types
    if (request.action === 'ping') {
      console.log('BG: External ping request received');
      sendResponse({
        success: true,
        version: chrome.runtime.getManifest().version,
        extensionId: chrome.runtime.id
      });
      return;
    } else if (request.action === 'startSearch') {
      console.log('BG: External startSearch request received');

      // Call the internal search handler
      handleStartSearch(request, sendResponse);
      return true; // Indicate async response
    } else if (request.action === 'getSearchResults') {
      console.log('BG: External getSearchResults request received');

      // Call the internal handler
      handleGetSearchResults(request, sendResponse);
      return false; // Synchronous response
    } else if (request.action === 'checkStayfuConnection') {
      console.log('BG: External checkStayfuConnection request received');

      // Call the internal handler
      handleCheckStayfuConnection(request, sendResponse);
      return true; // Asynchronous response
    } else if (request.action === 'updatePropertyData') {
      console.log('BG: External updatePropertyData request received');

      // Call the internal handler
      handleUpdatePropertyData(request, sendResponse);
      return true; // Asynchronous response
    }

    // Unknown action
    console.warn(`BG: Unhandled external action: ${request.action}`);
    sendResponse({ success: false, error: `Unknown action: ${request.action}` });
  }
);

// Open settings page on icon click
chrome.action.onClicked.addListener(() => {
    chrome.runtime.openOptionsPage();
});

// --- Message Listener ---
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    const action = request?.action;
    console.log(`BG: Received action: "${action}"`);

    // Special case for product data from content script
    if (action === 'productData') {
        handleProductData(request, sender, sendResponse);
        return true;
    }

    if (action === 'ping') {
        console.log('BG: Responding to ping from:', sender.url);
        if (!sender.url) {
            console.warn('BG: Ping received from unknown origin');
            sendResponse({ success: false, error: 'Invalid origin' });
            return true;
        }

        try {
            const response = {
                success: true,
                version: '2.0.1',
                extensionId: chrome.runtime.id,
                manifest: chrome.runtime.getManifest()
            };
            console.log('BG: Ping response:', response);
            sendResponse(response);
        } catch (error) {
            console.error('BG: Ping response error:', error);
            sendResponse({
                success: false,
                error: error.message,
                extensionId: chrome.runtime.id
            });
        }
        return true;
    }

    let handlerFunction = null;
    let isAsync = false;

    // Map actions to handler functions
    if (action === 'getStayfuSettings') {
        handlerFunction = handleGetStayfuSettings;
        isAsync = false; // It's synchronous
    } else if (action === 'checkStayfuConnection') {
        handlerFunction = handleCheckStayfuConnection;
        isAsync = true; // It's asynchronous
    } else if (action === 'setStayfuUrl') {
        handlerFunction = handleSetStayfuUrl;
        isAsync = true; // It's asynchronous
    } else if (action === 'setStayfuToken') {
        handlerFunction = handleSetStayfuToken;
        isAsync = true; // It's asynchronous
    } else if (action === 'startSearch') {
        handlerFunction = handleStartSearch;
        isAsync = true; // It's asynchronous
    } else if (action === 'getSearchStatus') {
        handlerFunction = handleGetSearchStatus;
        isAsync = false; // It's synchronous
    } else if (action === 'getSearchResults') {
        handlerFunction = handleGetSearchResults;
        isAsync = false; // It's synchronous
    }

    console.log(`BG: Checking handler for action "${action}". Handler assigned: ${typeof handlerFunction === 'function'}`); // Add log
    if (typeof handlerFunction === 'function') {
        console.log(`BG: Executing handler for action "${action}".`);
        try {
            const result = handlerFunction(request, sendResponse);
            if (!isAsync && result !== undefined) {
                console.warn(`BG: Sync handler for "${action}" returned a value directly.`);
            }
        } catch (error) {
            console.error(`BG: Error executing handler for action "${action}":`, error);
            try {
                sendResponse({ success: false, error: error.message || 'An unexpected error occurred' });
            } catch (e) {
                console.error("BG: Error calling sendResponse:", e);
            }
            isAsync = false;
        }
        return isAsync; // Return true for async handlers
    } else {
        console.warn(`BG: No handler found for action "${action}"`);
        try {
            sendResponse({ success: false, error: `Unknown action: ${action}` });
        } catch (e) {
            console.error("BG: Error calling sendResponse for unknown action:", e);
        }
        return false;
    }
});

// --- Initialization ---
// Load configuration on startup
loadConfig();

// Clear any previous search data
chrome.storage.local.remove(['searchMeta', 'products']);
