<!DOCTYPE html>
<html>
<head>
    <title>StayFu Extension Settings</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="logo">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
            StayFu Amazon Import
        </div>

        <div class="section">
            <h2>Connection Status</h2>
            <div id="connection-status" class="alert alert-warning">
                Checking connection to StayFu...
            </div>

            <div class="form-group">
                <label for="stayfuUrl">StayFu URL:</label>
                <input type="url" id="stayfuUrl" class="form-control" placeholder="https://yourdomain.com or http://localhost:8080">
                <small>The URL where your StayFu application is hosted</small>
            </div>

            <div class="form-group">
                <label for="stayfuApiToken">API Token:</label>
                <input type="password" id="stayfuApiToken" class="form-control" placeholder="Your StayFu API token">
                <small>Generate this token in your StayFu application settings</small>
            </div>

            <div class="button-group">
                <button id="saveSettings" class="button-primary">Save Settings</button>
                <button id="testConnection" class="button-secondary">Test Connection</button>
            </div>
        </div>

        <div class="section">
            <h2>Extension Info</h2>
            <div class="info-grid">
                <div>
                    <label>Version:</label>
                    <span id="version">2.0.0</span>
                </div>
                <div>
                    <label>Status:</label>
                    <span id="status">Active</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>How It Works</h2>
            <p>
                This extension automatically scrapes Amazon products based on search terms entered in the StayFu inventory dashboard.
            </p>
            <ol class="instructions">
                <li>Configure the extension with your StayFu URL</li>
                <li>Go to the Amazon Search tab in your StayFu inventory dashboard</li>
                <li>Enter a search term and click "Search Amazon"</li>
                <li>The extension will search Amazon and send results back to StayFu</li>
                <li>Select products to import and assign them to properties/collections</li>
            </ol>
        </div>
    </div>
    <script src="settings.js"></script>
</body>
</html>