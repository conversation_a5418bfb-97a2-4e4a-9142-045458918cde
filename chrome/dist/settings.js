document.addEventListener('DOMContentLoaded', function() {
    const statusElement = document.getElementById('connection-status');
    const urlInput = document.getElementById('stayfuUrl');
    const tokenInput = document.getElementById('stayfuApiToken');
    const saveButton = document.getElementById('saveSettings');
    const testButton = document.getElementById('testConnection');
    const versionElement = document.getElementById('version');
    const statusTextElement = document.getElementById('status');

    // Set version from manifest
    chrome.runtime.getManifest().version && (versionElement.textContent = chrome.runtime.getManifest().version);

    // Load initial settings from background script
    chrome.runtime.sendMessage({ action: 'getStayfuSettings' }, function(response) {
        if (chrome.runtime.lastError) {
            console.error("Error getting settings:", chrome.runtime.lastError);
            updateStatus('Error loading settings.', 'error');
            return;
        }
        if (response) {
            if (response.url) {
                urlInput.value = response.url;
            }
            if (response.hasToken) {
                tokenInput.placeholder = '•••••••••••••••• (Token is set)';
            } else {
                tokenInput.placeholder = 'Your StayFu API token';
            }
            // Perform initial connection check after loading settings
            checkConnection();
        } else {
            updateStatus('Could not load settings from background.', 'error');
        }
    });

    // Save settings via background script
    saveButton.addEventListener('click', async function() {
        const url = urlInput.value.trim();
        const token = tokenInput.value.trim(); // Get token only if entered

        if (!url) {
            updateStatus('Please enter the StayFu URL', 'error');
            return;
        }

        // Show saving state
        saveButton.textContent = 'Saving...';
        saveButton.disabled = true;
        updateStatus('Saving settings...', 'warning');

        try {
            // First save URL
            await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'setStayfuUrl',
                    url: url
                }, response => {
                    if (!response?.success) {
                        reject(new Error(response?.error || 'Failed to save URL'));
                    }
                    resolve();
                });
            });

            // If a new token was entered, save and validate it
            if (token) {
                await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({
                        action: 'setStayfuToken',
                        token: token
                    }, response => {
                        if (!response?.success) {
                            reject(new Error(response?.error || 'Failed to validate token'));
                        }
                        resolve();
                    });
                });

                // Clear token input after successful save
                tokenInput.value = '';
                tokenInput.placeholder = '•••••••••••••••• (Token is set)';
            }

            updateStatus('Settings saved successfully!', 'success');

            // Re-check connection
            setTimeout(checkConnection, 1000);

        } catch (error) {
            console.error('Error saving settings:', error);
            updateStatus(error.message, 'error');
            // If token validation failed, don't clear the input to allow user to fix it
            if (!error.message.includes('token')) {
                tokenInput.value = '';
            }
        } finally {
            saveButton.textContent = 'Save Settings';
            saveButton.disabled = false;
        }
    });

    // Test connection via background script
    testButton.addEventListener('click', checkConnection);

    // Function to check connection via background script
    function checkConnection() {
        updateStatus('Testing connection...', 'warning');
        testButton.disabled = true;
        testButton.textContent = 'Testing...';
        statusTextElement.textContent = 'Checking...';

        chrome.runtime.sendMessage({ action: 'checkStayfuConnection' }, function(response) {
            testButton.disabled = false;
            testButton.textContent = 'Test Connection';

            if (chrome.runtime.lastError) {
                console.error("Error checking connection:", chrome.runtime.lastError);
                updateStatus(`Connection check failed: ${chrome.runtime.lastError.message}`, 'error');
                statusTextElement.textContent = 'Error';
                return;
            }

            if (response && response.connected) {
                updateStatus('Connected to StayFu successfully', 'success');
                statusTextElement.textContent = 'Connected';
            } else {
                const errorMsg = response?.error || 'Could not connect. Check URL and Token.';
                updateStatus(`Connection failed: ${errorMsg}`, 'error');
                statusTextElement.textContent = 'Disconnected';

                // If token is invalid, clear the placeholder to prompt for new token
                if (errorMsg.includes('token') || errorMsg.includes('auth')) {
                    tokenInput.placeholder = 'Enter new API token';
                }
            }
        });
    }

    // Function to update status message display
    function updateStatus(message, type) {
        if (!statusElement) return;

        statusElement.textContent = message;
        statusElement.className = `alert alert-${type}`;

        // Scroll status into view if it's an error
        if (type === 'error') {
            statusElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    // Check for active searches every 5 seconds
    setInterval(() => {
        chrome.runtime.sendMessage({ action: 'getSearchStatus' }, function(response) {
            if (response && response.isSearching) {
                statusTextElement.textContent = 'Searching Amazon...';
            } else if (statusTextElement.textContent === 'Searching Amazon...') {
                statusTextElement.textContent = 'Connected';
            }
        });
    }, 5000);
});
